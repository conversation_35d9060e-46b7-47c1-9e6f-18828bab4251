package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.router.VueRouter;
import com.botong.entity.system.Menu;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public interface MenuService extends IService<Menu> {

    /**
     * 获取用户权限
     *
     * @param username 用户名
     * @return 用户权限
     */
    String findUserPermissions(String username);

    /**
     * 获取菜单列表
     *
     * @param title 查询参数
     * @return 菜单集合
     */
    List<VueRouter<Menu>> getList(String title);

    /**
     * 根据主键获取菜单详情
     *
     * @param menuId 主键
     * @return 菜单实体
     */
    Menu info(Long menuId);

    /**
     * 新增菜单
     *
     * @param menu 实体
     */
    void add(Menu menu);

    /**
     * 根据主键修改信息
     *
     * @param menu 实体
     */
    void update(Menu menu);

    /**
     * 根据主键删除菜单信息
     *
     * @param menuIds 菜单id集合
     */
    void delete(List<Long> menuIds);

    /**
     * 获取用户路由
     *
     * @param username 用户名
     * @return 用户路由
     */
    List<VueRouter<Menu>> getUserRouters(String username);
}

