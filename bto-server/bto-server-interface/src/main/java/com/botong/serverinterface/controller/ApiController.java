package com.botong.serverinterface.controller;

import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.InverterRtdataDetailRecord;
import com.botong.entity.serverinterface.entity.PlantInfoVO;
import com.botong.entity.serverinterface.vo.InverterDayElectricityVO;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ProjectEnum;
import com.botong.serverinterface.pojo.dto.*;
import com.botong.serverinterface.pojo.vo.InverterAlarmVO;
import com.botong.serverinterface.pojo.vo.WorkOrderVO;
import com.botong.serverinterface.service.InverterRtdataDetailRecordService;
import com.botong.serverinterface.service.PlantInfoService;
import com.botong.utils.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2024/4/25.
 */

@Api(tags = "枫电API接口")
@RestController
@RequestMapping("api")
public class ApiController {

    @Resource
    private PlantInfoService plantInfoService;

    @Resource
    private InverterRtdataDetailRecordService inverterRtdataDetailRecordService;

    @ApiOperation("逆变器信息查询")
    @PostMapping("/inverter")
    public ResultVo<CommonPage<InverterRtdataDetailRecord>> queryInverterDetailRecordPages(@RequestBody InverterQueryDTO query) {
        Map<String, Object> currentUserInfo = JwtUtil.getCurrentUserInfo();
        if (!ProjectEnum.getNameByCode(query.getSpecial()).equals(currentUserInfo.get("username"))) {
            return ResultVo.fail("参数有误");
        }
        CommonPage<InverterRtdataDetailRecord> pageVo = inverterRtdataDetailRecordService.queryInverterDetailRecordPages(query);
        return ResultVo.success(pageVo);
    }



    @ApiOperation("电站信息查询")
    @PostMapping("/plant")
    public ResultVo<CommonPage<PlantInfoVO>> getPlantInfoPage(@RequestBody PlantQueryDTO query) {
        Map<String, Object> currentUserInfo = JwtUtil.getCurrentUserInfo();
        if (!ProjectEnum.getNameByCode(query.getSpecial()).equals(currentUserInfo.get("username"))) {
            return ResultVo.fail("参数有误");
        }
        CommonPage<PlantInfoVO> pageVo = plantInfoService.getPlantInfoPage(query);
        return ResultVo.success(pageVo);
    }

    @ApiOperation("工单信息查询")
    @PostMapping("/work")
    public ResultVo<CommonPage<WorkOrderVO>> getWorkOrderPage(@RequestBody WorkOrderDTO query) {
        CommonPage<WorkOrderVO> pageVo = plantInfoService.getWorkOrderPage(query);
        return ResultVo.success(pageVo);
    }

    @ApiOperation("获取逆变器告警信息")
    @PostMapping("/getInverterAlarm")
    public ResultVo<CommonPage<InverterAlarmVO>> getInverterAlarm(@RequestBody InverterAlarmDTO inverterAlarmDTO) {
        Map<String, Object> currentUserInfo = JwtUtil.getCurrentUserInfo();
        if (!ProjectEnum.getNameByCode(inverterAlarmDTO.getSpecial()).equals(currentUserInfo.get("username"))) {
            return ResultVo.fail("参数有误");
        }
        CommonPage<InverterAlarmVO> alarmList = plantInfoService.getInverterAlarm(inverterAlarmDTO);
        return ResultVo.success(alarmList);
    }

    @ApiOperation("获取历史发电记录")
    @PostMapping("/getHistoryElectricity")
    public ResultVo<CommonPage<InverterDayElectricityVO>> getHistoryElectricity(@RequestBody InverterDayElectricityDTO inverterDayMidDTO) {
        Map<String, Object> currentUserInfo = JwtUtil.getCurrentUserInfo();
        if (!ProjectEnum.getNameByCode(inverterDayMidDTO.getSpecial()).equals(currentUserInfo.get("username"))) {
            return ResultVo.fail("参数有误");
        }
        CommonPage<InverterDayElectricityVO> list = plantInfoService.getHistoryElectricity(inverterDayMidDTO);
        return ResultVo.success(list);
    }


}
