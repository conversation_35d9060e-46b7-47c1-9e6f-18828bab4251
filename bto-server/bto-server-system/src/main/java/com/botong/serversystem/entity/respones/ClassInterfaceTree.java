package com.botong.serversystem.entity.respones;

import com.baomidou.mybatisplus.annotation.TableField;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class ClassInterfaceTree {
    @ApiModelProperty(value = "类别ID")
    private Long classId;
    @TableField(value = "ROLE_NAME")
    @ApiModelProperty(value = "类别名称")
    private String className;
    private List<InterfaceNameVO> interfaceTreeList;
}