package com.botong.serversystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.entity.serverinterface.entity.DeptInterface;
import com.botong.serversystem.entity.respones.DeptInterfaceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * (DeptInterface)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-03 14:46:50
 */
public interface DeptInterfaceMapper extends BaseMapper<DeptInterface> {

    /**
     * 按部门id列出
     *
     * @param deptId 部门ID
     * @return {@link List }<{@link DeptInterfaceVO }>
     * <AUTHOR>
     * @since 2023-08-03 16:42:08
     */
    List<DeptInterfaceVO> listByDeptId(@Param("deptId") Long deptId);
}

