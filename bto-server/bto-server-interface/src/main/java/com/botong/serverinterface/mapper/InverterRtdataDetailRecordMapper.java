package com.botong.serverinterface.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.entity.serverinterface.entity.InverterRtdataDetailRecord;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;


/**
 * (InverterRtdataDetailRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-14 16:08:50
 */
public interface InverterRtdataDetailRecordMapper extends BaseMapper<InverterRtdataDetailRecord> {

    InverterRtdataDetailRecord getSumByInverterIds(@Param("list") ArrayList<String> list);
}

