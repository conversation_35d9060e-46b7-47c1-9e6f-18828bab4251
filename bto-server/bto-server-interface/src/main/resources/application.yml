server:
  port: 8202

spring:
  application:
    name: BTO-Server-Interface
  profiles:
   active: prod


security:
  oauth2:
    resource:
      id: ${spring.application.name}
      user-info-uri: http://localhost:8301/auth/user
feign:
  compression:
    request:
      enabled: false
    response:
      enabled: false
  hystrix:
    enabled: true
  httpclient:
    enabled: true
  client:
    config:
      default:
        logger-level: full
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 6000
  shareSecurityContext: true
info:
  app:
    name: ${spring.application.name}
    description: "@project.description@"
    version: "@project.version@"
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
ribbon:
  ReadTimeout: 6000
  ConnectTimeout: 6000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 1
logging:
  level:
    com.botong.serverinterface.feign: debug
