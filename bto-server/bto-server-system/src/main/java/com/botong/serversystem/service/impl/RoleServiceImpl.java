package com.botong.serversystem.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.system.Menu;
import com.botong.entity.system.Role;
import com.botong.entity.system.RoleMenu;
import com.botong.enums.ResultCode;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.requerst.RoleMenuDTO;
import com.botong.serversystem.entity.requerst.RoleQueryDTO;
import com.botong.serversystem.entity.respones.RoleNameVO;
import com.botong.serversystem.entity.respones.RoleVo;
import com.botong.serversystem.mapper.RoleMapper;
import com.botong.serversystem.service.MenuService;
import com.botong.serversystem.service.RoleMenuService;
import com.botong.serversystem.service.RoleService;
import com.botong.utils.BeanCopyUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    private final RoleMenuService roleMenuService;
    private final MenuService menuService;

    @Override
    public PageInfo<RoleVo> getList(RoleQueryDTO query) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StringUtils.isNotBlank(query.getRoleName()), Role::getRoleName, query.getRoleName());
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<Role> roles = this.baseMapper.selectList(queryWrapper);
        ArrayList<RoleVo> roleVos = new ArrayList<>();
        for (Role role : roles) {
            RoleVo roleVo = new RoleVo();
            BeanUtils.copyProperties(role, roleVo);
            roleVo.setMenus(getMenuListByRoleId(roleVo.getRoleId()));
            roleVos.add(roleVo);
        }

        PageInfo<Role> source = new PageInfo<>(roles);
        PageInfo<RoleVo> target = new PageInfo<>();
        BeanUtils.copyProperties(source, target);
        target.setList(roleVos);
        return target;
    }

    @Override
    public Role info(Long roleId) {
        return getById(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Role role) {
        boolean saveResult = save(role);
        if (Boolean.FALSE.equals(saveResult)) {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Role role) {
        boolean updateResult = updateById(role);
        if (Boolean.FALSE.equals(updateResult)) {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> roleIds) {

        boolean removeResult = removeByIds(roleIds);
        if (Boolean.FALSE.equals(removeResult)) {
            throw new BtoException(ResultCode.FAIL);
        }
        // 删除角色与菜单关系
        roleMenuService.deleteRoleMenusByRoleId(roleIds);

        // todo 删除角色与接口的关系


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void authorizationMenu(RoleMenuDTO roleMenuDTO) {
        roleMenuService.authorizationMenu(roleMenuDTO);
    }

    @Override
    public List<Menu> getMenuListByRoleId(Long roleId) {
        List<RoleMenu> roleMenus = roleMenuService.getRoleMenusByRoleId(roleId);
        List<Long> menuIds = roleMenus.stream().map(RoleMenu::getMenuId).collect(Collectors.toList());
        if (menuIds.isEmpty()) {
            return null;
        }
        return menuService.listByIds(menuIds);
    }

    @Override
    public List<RoleNameVO> getRoleNameList(RoleQueryDTO roleQueryDTO) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ObjectUtil.isNotNull(roleQueryDTO),Role::getRoleName,roleQueryDTO.getRoleName());
        List<Role> roles = baseMapper.selectList(queryWrapper);
        List<RoleNameVO> roleNameList = BeanCopyUtils.copyBeanList(roles, RoleNameVO.class);
        return roleNameList;
    }

}
