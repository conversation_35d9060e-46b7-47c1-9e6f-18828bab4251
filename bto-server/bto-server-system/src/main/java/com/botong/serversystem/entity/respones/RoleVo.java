package com.botong.serversystem.entity.respones;

import com.botong.entity.system.Menu;
import com.botong.entity.system.Role;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/22.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class RoleVo extends Role {
    private static final long serialVersionUID = -2243228892271465889L;
    @ApiModelProperty(value = "角色拥有菜单")
    private List<Menu> menus;
}
