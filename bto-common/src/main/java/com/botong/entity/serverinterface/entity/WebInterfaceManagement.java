package com.botong.entity.serverinterface.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.enums.RequestMethodEnum;
import com.botong.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * web接口管理表(WebInterfaceManagement)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-12 14:13:35
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("t_interface_info")
public class WebInterfaceManagement  {
    @TableId
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("接口描述")
    @Size(max = 85, message = "描述过长，最大长度为85")
    private String interfaceDescribe;
    @ApiModelProperty("接口请求方式，1:post,2:get,3:put,4:delete")
    @JsonFormat
    private RequestMethodEnum requestMethod;
    @ApiModelProperty("接口url")
    private String url;
    @ApiModelProperty("请求头")
    private String requestHeader;
    @ApiModelProperty("响应头")
    private String responseHeader;
    @ApiModelProperty("接口状态，0:失效，1:正常")
    @JsonFormat
    private StatusEnum status;
    @ApiModelProperty("创建时间 yy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @ApiModelProperty("修改时间 yy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime  modifyTime;
}

