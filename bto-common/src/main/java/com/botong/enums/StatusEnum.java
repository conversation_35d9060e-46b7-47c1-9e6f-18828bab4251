package com.botong.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum StatusEnum {
    /**
     * 空字符串 “”
     */
    NULL("-2", ""),

    /**
     * 未知
     */
    UNKNOWN("-1", "UNKNOWN"),

    /**
     * 失效
     */
    INVALID("0", "失效"),
    /**
     * 正常
     */
    VALID("1", "正常");


    private final String code;
    @EnumValue
    private final String name;

    StatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonCreator
    public static StatusEnum fromValue(String value) {
        if (StrUtil.isEmpty(value)) {
            return StatusEnum.NULL;
        }
        for (StatusEnum statusEnum : StatusEnum.values()) {
            if (StrUtil.equals(statusEnum.getCode(), value)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("Invalid status value : " + value);
    }
}