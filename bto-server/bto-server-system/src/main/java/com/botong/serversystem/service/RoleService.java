package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.system.Menu;
import com.botong.entity.system.Role;
import com.botong.serversystem.entity.requerst.RoleMenuDTO;
import com.botong.serversystem.entity.requerst.RoleQueryDTO;
import com.botong.serversystem.entity.respones.RoleNameVO;
import com.botong.serversystem.entity.respones.RoleVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public interface RoleService extends IService<Role> {

    /**
     * 获取角色列表
     *
     * @param roleQueryDTO 查询参数
     * @return 角色集合
     */
    PageInfo<RoleVo> getList(RoleQueryDTO roleQueryDTO);

    /**
     * 根据主键获取角色详情
     *
     * @param roleId 主键
     * @return 角色实体
     */
    Role info(Long roleId);

    /**
     * 新增角色
     *
     * @param role 实体
     */
    void add(Role role);

    /**
     * 根据主键修改信息
     *
     * @param role 实体
     */
    void update(Role role);

    /**
     * 根据主键删除角色信息
     *
     * @param roleIds 角色id集合
     */
    void delete(List<Long> roleIds);

    /**
     * 根据角色id授权角色菜单
     *
     * @param roleMenuDTO 角色id集合
     */
    void authorizationMenu(RoleMenuDTO roleMenuDTO);

    /**
     * 根据角色id获取所拥有菜单
     *
     * @param roleId 主键
     * @return 菜单集合
     */
    List<Menu> getMenuListByRoleId(Long roleId);

    /**
     * 获取角色名和接口名
     * @param roleQueryDTO
     * @return
     */
    List<RoleNameVO> getRoleNameList(RoleQueryDTO roleQueryDTO);
}

