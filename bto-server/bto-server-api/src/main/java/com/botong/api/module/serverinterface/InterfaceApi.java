package com.botong.api.module.serverinterface;

import com.botong.api.fallback.InterfaceClientFallback;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import com.botong.constant.BtoServerConstant;
import com.botong.entity.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 接口客户端
 *
 * <AUTHOR>
 * @since 2023-08-05 09:22:15
 */
@Configuration
@FeignClient(value = BtoServerConstant.BTO_SERVER_INTERFACE, fallbackFactory = InterfaceClientFallback.class)
public interface InterfaceApi {
    /**
     * 获取接口信息
     *
     * @return {@link ResultVo }<{@link List }<{@link InterfaceNameVO }>>
     * <AUTHOR>
     * @since 2023-08-05 20:49:21
     */
    @PostMapping("webInterfaceManagement/getInterfaceInfo")
    ResultVo<List<InterfaceNameVO>> getInterfaceName();

    /**
     * 获取接口信息重复
     *
     * @return {@link ResultVo }<{@link List }<{@link InterfaceNameVO }>>
     * <AUTHOR>
     * @since 2023-08-08 09:05:33
     */
    @PostMapping("webInterfaceManagement/getInterfaceInfoRepeat")
    ResultVo<List<InterfaceNameVO>> getInterfaceInfoRepeat();

}