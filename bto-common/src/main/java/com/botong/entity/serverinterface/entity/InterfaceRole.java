package com.botong.entity.serverinterface.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
/**
 * 接口角色关联表(InterfaceRole)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-18 09:04:12
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_interface_role")
public class InterfaceRole  {
    @TableId
    private Long id;
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("角色id")
    private Long roleId;
    @ApiModelProperty("创建时间 yy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @ApiModelProperty("修改时间 yy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime  modifyTime;
    public InterfaceRole(Long interfaceId, Long roleId) {
        this.interfaceId = interfaceId;
        this.roleId = roleId;
    }
}

