package com.botong.serverinterface.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.CommonPage;
import com.botong.serverinterface.pojo.dto.InverterQueryDTO;
import com.botong.entity.serverinterface.entity.InverterRtdataDetailRecord;


/**
 * (InverterRtdataDetailRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-14 16:08:50
 */
public interface InverterRtdataDetailRecordService extends IService<InverterRtdataDetailRecord> {

    /**
     * 逆变器分页查询
     *
     * @param currentPage
     * @param size
     * @param specialId
     * @return
     */
    CommonPage<InverterRtdataDetailRecord> queryInverterDetailRecordPages(Integer currentPage, Integer size, Integer specialId);

    /**
     * 逆变器分页查询
     *
     * @param query
     */
    CommonPage<InverterRtdataDetailRecord> queryInverterDetailRecordPages(InverterQueryDTO query);
}

