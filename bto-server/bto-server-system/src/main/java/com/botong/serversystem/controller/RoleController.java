package com.botong.serversystem.controller;

import com.botong.entity.CommonPage;
import com.botong.entity.system.Role;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.serversystem.entity.requerst.RoleMenuDTO;
import com.botong.serversystem.entity.requerst.RoleQueryDTO;
import com.botong.serversystem.entity.respones.RoleNameVO;
import com.botong.serversystem.entity.respones.RoleVo;
import com.botong.serversystem.service.RoleService;
import com.botong.utils.NullUtils;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
@RequestMapping("role")
@Api(tags = "角色管理")
@Slf4j
@Validated
@RequiredArgsConstructor
public class RoleController {
    private final RoleService roleService;

    /**
     * 获取角色列表
     *
     * @param roleQueryDTO 查询参数
     * @return 角色集合
     */
    @ApiOperation("获取角色列表")
    @PostMapping("/list")
    public ResultVo<CommonPage<RoleVo>> getList(@RequestBody RoleQueryDTO roleQueryDTO) {
        PageInfo<RoleVo> roles = roleService.getList(roleQueryDTO);
        return ResultVo.success(CommonPage.pageInfo(roles));
    }

    /**
     * 根据主键获取角色详情
     *
     * @param roleId 主键
     * @return 角色实体
     */
    @ApiOperation("根据主键获取角色详情")
    @GetMapping("/info/{roleId}")
    public ResultVo<Role> info(@PathVariable("roleId") Long roleId) {
        boolean result = NullUtils.isNull(roleId);
        if (result) {
            String message = "roleId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            Role role = roleService.info(roleId);
            return ResultVo.success(role);
        }
    }

    /**
     * 新增角色
     *
     * @param role 实体
     */
    @ApiOperation("新增角色")
    @PostMapping("/add")
    public ResultVo<Void> add(@RequestBody Role role) {
        roleService.add(role);
        return ResultVo.success(ResultCode.ADD_SUCCESS);
    }


    /**
     * 根据主键修改信息
     *
     * @param role 实体
     */
    @ApiOperation("根据主键修改信息")
    @PostMapping("/update")
    public ResultVo<Void> update(@RequestBody Role role) {
        boolean result = NullUtils.isNull(role.getRoleId());
        if (result) {
            String message = "roleId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            roleService.update(role);
            return ResultVo.success(ResultCode.EDIT_SUCCESS);
        }
    }


    /**
     * 根据主键删除角色信息
     *
     * @param roleIds 角色id集合
     */
    @ApiOperation("批量删除")
    @PostMapping("/delete")
    public ResultVo<Void> delete(@RequestBody List<Long> roleIds) {
        boolean result = NullUtils.isNull(roleIds);
        if (result) {
            String message = "roleIds must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        }
        roleService.delete(roleIds);
        return ResultVo.success(ResultCode.DELETE_SUCCESS);
    }

    /**
     * 根据角色id授权角色菜单
     *
     * @param roleMenuDTO 角色id集合
     */
    @ApiOperation("根据角色id授权角色菜单")
    @PostMapping("/authorizationMenu")
    public ResultVo<Void> authorizationMenu(@RequestBody RoleMenuDTO roleMenuDTO) {
        roleService.authorizationMenu(roleMenuDTO);
        return ResultVo.success();
    }


    @ApiOperation("获取角色名List")
    @PostMapping("/getRoleNameList")
    public ResultVo<List<RoleNameVO>> getRoleNameList(@RequestBody(required = false) RoleQueryDTO roleQueryDTO) {
        return ResultVo.success(roleService.getRoleNameList(roleQueryDTO));
    }

}