package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.Classification;
import com.botong.entity.vo.ResultVo;
import com.botong.serversystem.entity.requerst.AddClassDTO;
import com.botong.serversystem.entity.requerst.ClassQueryDTO;
import com.botong.serversystem.entity.respones.ClassificationVO;

import java.util.List;

/**
 * 分类服务
 *
 * <AUTHOR>
 * @since 2023-08-05 20:44:52
 */
public interface ClassificationService extends IService<Classification> {
    /**
     * 添加类别
     * @param addClassDTO
     * @return
     */
    ResultVo addClass(AddClassDTO addClassDTO);

    /**
     * 通过id删除类别
     * @param classId
     * @return
     */
    ResultVo deleteClassById(List<Long> classId);

    /**
     * 通过id修改类别
     * @param updateClassDTO
     * @return
     */
    ResultVo updateClass(ClassificationVO updateClassDTO);


    /**
     * 类别列表查询
     *
     * @param classQueryDTO
     * @param deptId        部门id
     * @return {@link List }<{@link ClassificationVO }>
     * <AUTHOR>
     * @since 2023-08-04 16:57:54
     */
    List<ClassificationVO> list(ClassQueryDTO classQueryDTO, Long deptId);

    /**
     * 类别页
     *
     * @param classQueryDTO 类查询dto
     * @return {@link CommonPage }<{@link ClassificationVO }>
     * <AUTHOR>
     * @since 2023-08-05 20:48:09
     */
    CommonPage<ClassificationVO> selectAll(ClassQueryDTO classQueryDTO);

    /**
     * 根据接口id获取类别
     *
     * @param  interfaceId 接口id
     * @return {@link List }<{@link ClassificationVO }>
     * <AUTHOR>
     * @since 2023-08-05 20:46:38
     */
    List<ClassificationVO> listByInterfaceId(Long interfaceId);

    /**
     * 根据类别id获取类别信息
     *
     * @param  classId 类别id
     * @return <{@link ClassificationVO }>
     * <AUTHOR>
     * @since 2023-08-05 20:46:38
     */
    ClassificationVO selectOne(Long classId);
}
