package com.botong.serverinterface.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.CommonPage;
import com.botong.entity.dto.SortType;
import com.botong.entity.serverinterface.dto.RoleQueryDTO;
import com.botong.entity.vo.ResultVo;
import com.botong.serverinterface.pojo.dto.*;
import com.botong.entity.serverinterface.entity.WebInterfaceManagement;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import com.botong.serverinterface.pojo.vo.InterfaceVO;
import com.botong.serverinterface.pojo.vo.RoleInterfaceTree;

import java.util.List;


/**
 * web接口管理表(WebInterfaceManagement)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-12 14:15:08
 */
public interface WebInterfaceManagementService extends IService<WebInterfaceManagement> {

    /**
     * 分页查询接口数据
     *
     * @param interfaceQueryDTO
     * @param currentPage
     * @param size
     * @param sortType
     * @param deptId
     * @param classId
     * @return
     */
    CommonPage<InterfaceVO> queryInterfaceDataInPages(InterfaceQueryDTO interfaceQueryDTO, Integer currentPage, Integer size, SortType sortType, Long deptId, Long classId);


    /**
     * 添加接口
     *
     * @param interfaceDTO
     * @return
     */
    ResultVo addInterface(AddInterfaceDTO interfaceDTO);

    /**
     * 根据id修改接口
     *
     * @param interfaceDTO
     * @return
     */
    ResultVo updateByInterface(InterfaceDTO interfaceDTO);

    /**
     * 根据id删除接口
     *
     * @param ids
     * @return
     */
    ResultVo deleteByInterface(List<Long> ids);

    /**
     * 修改接口状态
     *
     * @param interfaceChangStatusDTO
     * @return ResultVo
     */
    ResultVo changeStatusById(InterfaceChangStatusDTO interfaceChangStatusDTO);


    /**
     * 给角色添加接口
     *
     * @param roleId
     * @param interfaceId
     * @return
     */
    ResultVo addInterfaceToRole(Long roleId, List<Long> interfaceId);


    /**
     * 获取角色接口
     * @param roleQueryDTO
     * @return
     */
    ResultVo<List<RoleInterfaceTree>> roleInterfaceTree(RoleQueryDTO roleQueryDTO);

    /**
     * 获取全部接口的名字和id
     * @return
     */
    ResultVo<List<InterfaceNameVO>> getInterfaceName();


    /**
     * 获取接口信息重复
     *
     * @return {@link ResultVo }<{@link List }<{@link InterfaceNameVO }>>
     * <AUTHOR>
     * @since 2023-08-08 08:35:18
     */
    ResultVo<List<InterfaceNameVO>> getInterfaceInfoRepeat();
}

