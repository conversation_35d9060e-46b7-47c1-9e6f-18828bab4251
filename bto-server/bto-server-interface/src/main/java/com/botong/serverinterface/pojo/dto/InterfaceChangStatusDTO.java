package com.botong.serverinterface.pojo.dto;

import com.botong.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/7/19 10:03
 */
@Data
@AllArgsConstructor
public class InterfaceChangStatusDTO {
    @NotNull(message = "接口id不能为空")
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @NotNull(message = "接口状态不能为空")
    @ApiModelProperty("接口状态 0:失效，1:正常")
    @JsonFormat
    private StatusEnum status;
}
