package com.botong.serverinterface.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.entity.serverinterface.entity.PlantInfoVO;
import com.botong.entity.serverinterface.vo.InverterDayElectricityVO;
import com.botong.serverinterface.pojo.dto.InverterAlarmDTO;
import com.botong.serverinterface.pojo.dto.InverterDayElectricityDTO;
import com.botong.serverinterface.pojo.dto.WorkOrderDTO;
import com.botong.serverinterface.pojo.vo.InverterAlarmVO;
import com.botong.serverinterface.pojo.vo.WorkOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * (YuexiuPlantInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:43
 */
public interface PlantInfoMapper extends BaseMapper<PlantInfoVO> {

    List<WorkOrderVO> getWorkOrderPage(@Param("query") WorkOrderDTO query);

    List<InverterAlarmVO> getInverterAlarm(@Param("query") InverterAlarmDTO query);

    List<InverterDayElectricityVO> getHistoryElectricity(@Param("query") InverterDayElectricityDTO query);
}

