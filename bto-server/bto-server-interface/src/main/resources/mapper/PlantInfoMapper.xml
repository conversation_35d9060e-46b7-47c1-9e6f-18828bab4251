<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.botong.serverinterface.mapper.PlantInfoMapper">

    <select id="getWorkOrderPage" resultType="com.botong.serverinterface.pojo.vo.WorkOrderVO">
        select
        work_id,
        status,
        order_id,
        plant_id,
        plant_name,
        source,
        alarm_time,
        alarm_level,
        create_time,
        update_time,
        alarm_type,
        issue_photo,
        unrepaired_cause,
        pre_repair_time,
        repair_time,
        alarm_cause,
        op_cause,
        priority,
        repair_photo_path,
        fault_photo_path,
        repair_status,
        alarm_info,
        alarm_device
        from wk_v_work_base_info
        left join wk_v_work_operation_info
        on wk_v_work_base_info.work_id = wk_v_work_operation_info.work_id
        <where>
            <if test="query.plantName!='' and query.plantName !=null">
                AND wk_v_work_base_info.plant_name = #{query.plantName}
            </if>
            <if test="query.workId!='' and query.workId !=null">
                AND wk_v_work_base_info.work_id = #{query.workId}
            </if>
            <if test="query.status!='' and query.status !=null">
                AND wk_v_work_base_info.status = #{query.status}
            </if>
            <if test="query.plantId!='' and query.plantId !=null">
                AND wk_v_work_base_info.plant_id = #{query.plantId}
            </if>
            <if test="query.alarmType!='' and query.alarmType !=null">
                AND wk_v_work_base_info.alarm_type = #{query.alarmType}
            </if>
        </where>
        order by update_time desc
    </select>
    <select id="getInverterAlarm" resultType="com.botong.serverinterface.pojo.vo.InverterAlarmVO">
        select
        plant_uid,
        plant_name,
        inverter_sn inverterId,
        special,
        alarm_code,
        alarm_info,
        alarm_level,
        status,
        start_time,
        end_time
        from bp_v_inverter_alarm_mid
        <where>
            <if test="query.special !=null">
                AND special = #{query.special}
            </if>
            <if test="query.plantName!='' and query.plantName !=null">
                AND plant_name = #{query.plantName}
            </if>
            <if test="query.startTime != null and query.startTime!='' and query.endTime != null and query.endTime!=''">
                AND date(start_time) BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
        </where>
        order by start_time desc
    </select>
    <select id="getHistoryElectricity" resultType="com.botong.entity.serverinterface.vo.InverterDayElectricityVO">
        select t2.plant_name,t1.plant_uid, t1.inverter_id, t1.collect_time, round(t1.today_electricity / 100,2 )
        today_electricity
        from bp_v_inverter_day_mid t1
        left join bp_v_plant_info t2 on t1.plant_uid = t2.plant_uid
        <where>
            <if test="query.special !=null">
                AND t1.special = #{query.special}
            </if>
            <if test="query.plantName !=null and query.plantName != ''">
                AND t2.plant_name = #{query.plantName}
            </if>
            <if test="query.plantUid !=null and query.plantUid != ''">
                AND t1.plant_uid = #{query.plantUid}
            </if>
            <if test="query.inverterId !=null and query.inverterId != ''">
                AND t1.inverter_id = #{query.inverterId}
            </if>
            <if test="query.startTime != null and query.startTime!='' and query.endTime != null and query.endTime!=''">
                AND date(t1.collect_time) BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
        </where>
        order by t1.collect_time desc
    </select>

</mapper>