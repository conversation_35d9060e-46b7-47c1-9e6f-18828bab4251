package com.botong.gateway.filter;

import com.alibaba.fastjson.JSONObject;
import com.botong.constant.BtoConstant;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.gateway.properties.BtoGatewayProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Base64Utils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.*;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
@Component
public class BtoGatewayRequestFilter implements GlobalFilter {

    @Autowired
    private BtoGatewayProperties properties;
    private AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        // 禁止客户端的访问资源逻辑
        Mono<Void> checkForbidUriResult = checkForbidUri(request, response);
        if (checkForbidUriResult != null) {
            return checkForbidUriResult;
        }

        // 日志打印
        printLog(exchange);

        byte[] token = Base64Utils.encode((BtoConstant.GATEWAY_TOKEN_VALUE).getBytes());
        ServerHttpRequest build = request.mutate().header(BtoConstant.GATEWAY_TOKEN_HEADER, new String(token)).build();
        ServerWebExchange newExchange = exchange.mutate().request(build).build();
        return chain.filter(newExchange);
    }

    private void printLog(ServerWebExchange exchange) {
        URI url = exchange.getAttribute(GATEWAY_REQUEST_URL_ATTR);
        ServerHttpRequest request = exchange.getRequest();
        String realIPAddress = request.getHeaders().getFirst("X-Forwarded-For");
        if (realIPAddress == null) {
            realIPAddress = request.getHeaders().getFirst("X-Real-IP");
        }

        if (realIPAddress == null) {
            // 如果以上头部都不存在，则使用请求的远程地址
            realIPAddress = request.getRemoteAddress().getAddress().getHostAddress();
        } else {
            // 如果使用了"X-Forwarded-For"，可能需要处理多个IP地址的情况
            // 通常，第一个IP是原始客户端的IP，但这也取决于代理的配置
            String[] forwardedIPs = realIPAddress.split(",");
            if (forwardedIPs.length > 0) {
                realIPAddress = forwardedIPs[0].trim();
            }
        }
        Route route = exchange.getAttribute(GATEWAY_ROUTE_ATTR);
        LinkedHashSet<URI> uris = exchange.getAttribute(GATEWAY_ORIGINAL_REQUEST_URL_ATTR);
        URI originUri = null;
        if (uris != null) {
            originUri = uris.stream().findFirst().orElse(null);
        }
        if (url != null && route != null && originUri != null) {
            log.info("转发请求：{}://{}{} --> 目标服务：{}，目标地址：{}://{}{}，转发时间：{}，IP地址：{}",
                    originUri.getScheme(), originUri.getAuthority(), originUri.getPath(),
                    route.getId(), url.getScheme(), url.getAuthority(), url.getPath(), LocalDateTime.now(),
                    realIPAddress);
        }
    }

    private Mono<Void> checkForbidUri(ServerHttpRequest request, ServerHttpResponse response) {
        String uri = request.getPath().toString();
        boolean shouldForward = true;
        String forbidRequestUri = properties.getForbidRequestUri();
        String[] forbidRequestUris = StringUtils.splitByWholeSeparatorPreserveAllTokens(forbidRequestUri, ",");
        if (forbidRequestUris != null && ArrayUtils.isNotEmpty(forbidRequestUris)) {
            for (String u : forbidRequestUris) {
                if (pathMatcher.match(u, uri)) {
                    shouldForward = false;
                }
            }
        }
        if (!shouldForward) {
            return makeResponse(response, ResultVo.fail(ResultCode.DO_NOT_ALLOW_ACCESS));
        }
        return null;
    }

    private Mono<Void> makeResponse(ServerHttpResponse response, ResultVo<ResultCode> resultVo) {
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, BtoConstant.APPLICATION_JSON_UTF8_VALUE);
        DataBuffer dataBuffer = response.bufferFactory().wrap(JSONObject.toJSONString(resultVo).getBytes());
        return response.writeWith(Mono.just(dataBuffer));
    }
}