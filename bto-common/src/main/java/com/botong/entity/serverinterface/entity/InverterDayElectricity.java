package com.botong.entity.serverinterface.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/11/21 9:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("bp_v_inverter_day_mid")
public class InverterDayElectricity {
    private String plantUid;
    private String inverterId;
    private String todayElectricity;
    private String special;

    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp collectTime;
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp dataTime;
}
