package com.botong.api.fallback;

import com.botong.api.module.serversystem.SystemApi;
import com.botong.entity.serverinterface.dto.RoleQueryDTO;
import com.botong.entity.serverinterface.vo.RoleNameVO;
import com.botong.entity.vo.ResultVo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
@Component
public class SystemServiceFallback implements FallbackFactory<SystemApi> {
    @Override
    public SystemApi create(Throwable throwable) {
        return new SystemApi() {
            @Override
            public String system(@RequestParam String name) {
                log.error("调用system服务出错", throwable);
                return "调用system服务出错";
            }

            @Override
            public ResultVo<List<RoleNameVO>> getRoleNameList(@RequestBody RoleQueryDTO roleQueryDTO) {
                log.error("调用system服务出错", throwable);
                return ResultVo.fail("调用system服务出错");
            }

            @Override
            public ResultVo<List<Long>> list(Long deptId) {
                log.error("调用system服务出错", throwable);
                return ResultVo.fail("调用system服务出错");
            }

            @Override
            public ResultVo<List<Long>> listByClass(Long classId) {
                log.error("调用system服务出错", throwable);
                return ResultVo.fail(Collections.emptyList());
            }

            @Override
            public ResultVo deleteDeptInterfaceByinterface(List<Long> interfaceIds) {
                log.error("调用system服务出错", throwable);
                return ResultVo.fail();
            }

            @Override
            public ResultVo deleteInterfaceClassByinterface(List<Long> interfaceIds) {
                log.error("调用system服务出错", throwable);
                return ResultVo.fail();

            }

            @Override
            public ResultVo insert(Long deptId, List<Long> interfaceIds) {
                log.error("调用system服务出错", throwable);
                return ResultVo.fail();

            }
        };
    }
}