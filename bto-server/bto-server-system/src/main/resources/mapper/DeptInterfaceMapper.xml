<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.botong.serversystem.mapper.DeptInterfaceMapper">
    <!--    List<DeptInterfaceVO> listByDeptId(@Param("deptIds") List<Long> deptIds);-->
    <select id="listByDeptId" resultType="com.botong.serversystem.entity.respones.DeptInterfaceVO">
        SELECT t1.dept_id, t2.interface_id, t2.name
        FROM t_interface_info t2
        LEFT JOIN t_dept_interface t1  ON t1.interface_id = t2.interface_id
        <if test="deptId != null and deptId != ''">
            WHERE t1.dept_id = #{deptId}
        </if>
    </select>


</mapper>