package com.botong.gateway.controller;

import com.botong.entity.vo.ResultVo;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
public class FallbackController {

    @RequestMapping("fallback/{name}")
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Mono<ResultVo<Object>> systemFallback(@PathVariable String name) {
        String response = String.format("%s服务繁忙，请稍后再试", name);
        return Mono.just(ResultVo.fail(response));
    }

}