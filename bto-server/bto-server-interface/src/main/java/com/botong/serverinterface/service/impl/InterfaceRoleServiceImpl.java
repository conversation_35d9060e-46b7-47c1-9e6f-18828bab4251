package com.botong.serverinterface.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.serverinterface.mapper.InterfaceRoleMapper;
import com.botong.entity.serverinterface.entity.InterfaceRole;
import com.botong.serverinterface.service.InterfaceRoleService;
import org.springframework.stereotype.Service;

/**
 * 接口角色关联表(InterfaceRole)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-18 09:04:17
 */
@Service("interfaceRoleService")
public class InterfaceRoleServiceImpl extends ServiceImpl<InterfaceRoleMapper, InterfaceRole> implements InterfaceRoleService {

}

