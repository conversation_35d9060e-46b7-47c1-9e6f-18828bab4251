package com.botong.entity.serverinterface.entity;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (VInverterRtdataDetailRecord)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 16:05:47
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("v_inverter_rtdata_detail_record")
public class InverterRtdataDetailRecord {
    private String inverterId;

    private String inverterTotalEnergy;

    private String inverterYearEnergy;

    private String inverterMonthEnergy;

    private String inverterTodayEnergy;

    private String inverterPower;

    private String inverterQpower;

    private String inverterPf;

    private String l1Volt;

    private String l1Curr;

    private String l1Freq;

    private String l1Dci;

    private String l1Power;

    private String l1Pf;

    private String l2Volt;

    private String l2Curr;

    private String l2Freq;

    private String l2Dci;

    private String l2Power;

    private String l2Pf;

    private String l3Volt;

    private String l3Curr;

    private String l3Freq;

    private String l3Dci;

    private String l3Power;

    private String l3Pf;

    private String pv1Volt;

    private String pv1Curr;

    private String pv2Volt;

    private String pv2Curr;

    private String pv3Volt;

    private String pv3Curr;

    private String pv4Volt;

    private String pv4Curr;

    private String pv5Volt;

    private String pv5Curr;

    private String pv6Volt;

    private String pv6Curr;

    private String cavityTemp;

    private Integer special;
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp inverterCurrentTime;

}

