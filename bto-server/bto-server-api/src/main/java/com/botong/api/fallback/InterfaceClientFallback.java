package com.botong.api.fallback;

import com.botong.api.module.serverinterface.InterfaceApi;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import com.botong.entity.vo.ResultVo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/5 11:24
 */
@Slf4j
@Component
public class InterfaceClientFallback implements FallbackFactory<InterfaceApi> {

    @Override
    public InterfaceApi create(Throwable cause) {
        return new InterfaceApi() {
            @Override
            public ResultVo<List<InterfaceNameVO>> getInterfaceName() {
                return ResultVo.fail("调用interface失败");
            }

            @Override
            public ResultVo<List<InterfaceNameVO>> getInterfaceInfoRepeat() {
                return ResultVo.fail("调用interface失败");
            }
        };
    }
}
