package com.botong.serverinterface.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/7/13 11:47
 */
@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class InterfaceTree {
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("角色id")
    private Long roleId;
}
