package com.botong.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
public enum ProjectEnum {

    ZHONG_SHAN_GONG_YONG(4300, "zsgyzhny"),

    YUE_XIU(4000, "yuexiu");


    private final Integer code;
    @EnumValue
    private final String username;

    ProjectEnum(Integer code, String username) {
        this.code = code;
        this.username = username;
    }

    public static String getNameByCode(Integer value) {
        for (ProjectEnum s : ProjectEnum.values()) {
            if (s.getCode().equals(value)) {
                return s.getUsername();
            }
        }
        return "";
    }

    public static Integer getCodeByName(String name) {
        for (ProjectEnum s : ProjectEnum.values()) {
            if (Objects.equals(s.getUsername(), name)) {
                return s.getCode();
            }
        }
        return null;
    }


}