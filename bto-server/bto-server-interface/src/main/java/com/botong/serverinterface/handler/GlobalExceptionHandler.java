package com.botong.serverinterface.handler;

import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.handler.BaseExceptionHandler;
import com.fasterxml.jackson.databind.exc.ValueInstantiationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
@RestControllerAdvice
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class GlobalExceptionHandler extends BaseExceptionHandler{

    /**
     * 方法参数非法异常
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultVo methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        ObjectError objectError = e.getBindingResult().getAllErrors().get(0);
        // System.out.println(objectError.getDefaultMessage());
        return ResultVo.fail(ResultCode.PARAM_ERROR,objectError.getDefaultMessage());
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultVo handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        Throwable cause = e.getCause();
        if (cause instanceof ValueInstantiationException) {
            Throwable innerCause = cause.getCause();
            if (innerCause instanceof IllegalArgumentException) {
                log.warn(innerCause.getMessage());
                return ResultVo.fail(innerCause.getMessage());
            }
        }
        log.warn("HTTP消息无法正确读取或解析 : "+e.getMessage());
        return ResultVo.fail("HTTP消息无法正确读取或解析");
    }
}