package com.botong.serversystem.service.impl;

import com.botong.entity.router.RouterMeta;
import com.botong.entity.router.VueRouter;
import com.botong.entity.system.Menu;
import com.botong.enums.ResultCode;
import com.botong.exception.BtoException;
import com.botong.serversystem.mapper.MenuMapper;
import com.botong.serversystem.service.MenuService;
import com.botong.utils.BaseTreeUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements MenuService {

    @Override
    public String findUserPermissions(String username) {
        List<Menu> userPermissions = this.baseMapper.findUserPermissions(username);
        return userPermissions.stream().map(Menu::getPerms).collect(Collectors.joining(","));
    }

    @Override
    public List<VueRouter<Menu>> getList(String title) {
        QueryWrapper<Menu> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StringUtils.isNotBlank(title), Menu::getTitle, title);
        queryWrapper.lambda().orderBy(true, false, Menu::getOrderNum);
        List<Menu> menus = this.baseMapper.selectList(queryWrapper);
        return buildRouters(menus);
    }

    @Override
    public Menu info(Long menuId) {
        return getById(menuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Menu menu) {
        boolean saveResult = save(menu);
        if (Boolean.FALSE.equals(saveResult)) {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Menu menu) {
        boolean updateResult = updateById(menu);
        if (Boolean.FALSE.equals(updateResult)) {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> menuIds) {
        boolean removeResult = removeByIds(menuIds);
        if (Boolean.FALSE.equals(removeResult)) {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    public List<Menu> findUserMenus(String username) {
        return this.baseMapper.findUserMenus(username);
    }

    public List<VueRouter<Menu>> getUserRouters(String username) {
        List<Menu> menus = this.findUserMenus(username);
        return buildRouters(menus);
    }

    public List<VueRouter<Menu>> buildRouters(List<Menu> menus) {
        List<VueRouter<Menu>> routes = new ArrayList<>();
        menus.forEach(menu -> {
            VueRouter<Menu> route = new VueRouter<>();
            route.setId(menu.getMenuId().toString());
            route.setParentId(menu.getParentId().toString());
            route.setPath(menu.getPath());
            route.setRedirect(menu.getRedirect());
            route.setComponent(menu.getComponent());
            route.setName(menu.getMenuName());
            route.setMeta(new RouterMeta(menu.getTitle(), menu.getIcon(), true, route.getHidden()));
            routes.add(route);
        });
        return BaseTreeUtil.buildVueRouter(routes);
    }
}
