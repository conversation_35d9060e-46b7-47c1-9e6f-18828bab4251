package com.botong.serversystem.controller;

import com.botong.entity.vo.ResultVo;
import com.botong.exception.BtoException;
import com.botong.serversystem.service.DeptInterfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * (DeptInterface)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-03 14:46:50
 */
@Api(tags = "部门接口管理")
@RestController
@RequestMapping("deptInterface")
public class DeptInterfaceController {



    @Resource
    private DeptInterfaceService deptInterfaceService;

    /**
     * 根据部门id展示接口
     *
     * @param deptId 部门id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-04 07:59:32
     */
    @ApiOperation("根据部门id获取接口id集合")
    @GetMapping("list/interfaceIds")
    public  ResultVo<List<Long>> list(@ApiParam(value = "部门id", required = false) @RequestParam(required = false) Long deptId) {
        if (deptId!= null && deptId == 0L) {
            throw new BtoException("请先绑定部门！");
        }
        return ResultVo.success(deptInterfaceService.list(deptId));
    }


    /**
     * 根据部门id添加接口
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 14:58:21
     */
    @ApiOperation("根据部门id添加接口")
    @PostMapping
    public ResultVo insert(@ApiParam(value = "部门id", required = true) @RequestParam Long deptId,
                           @ApiParam(value = "接口id列表", required = true) @RequestBody List<Long> interfaceIds) {
        if (Objects.isNull(deptId) || interfaceIds == null || interfaceIds.isEmpty()) {
            return ResultVo.fail("参数不能为空！");
        }
        return deptInterfaceService.insert(deptId, interfaceIds);
    }

    /**
     * 根据部门id和接口id删除对应数据
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 17:36:47
     */
    @ApiOperation("根据部门id和接口id删除对应数据")
    @DeleteMapping
    public ResultVo delete(@ApiParam(value = "部门id", required = true) @RequestParam Long deptId,
                           @ApiParam(value = "接口id列表", required = true) @RequestBody List<Long> interfaceIds) {
        if (Objects.isNull(deptId) || interfaceIds == null || interfaceIds.isEmpty()) {
            return ResultVo.fail("参数不能为空！");
        }
        return deptInterfaceService.delete(deptId, interfaceIds);
    }

    /**
     * 根据部门id和接口id更新对应数据
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 17:37:14
     */
    @ApiOperation("根据部门id和接口id更新对应数据")
    @PutMapping
    public ResultVo update(@ApiParam(value = "部门id", required = true) @RequestParam Long deptId,
                           @ApiParam(value = "接口id列表", required = true) @RequestBody List<Long> interfaceIds) {
        if (Objects.isNull(deptId) || interfaceIds == null || interfaceIds.isEmpty()) {
            return ResultVo.fail("参数不能为空！");
        }
        return deptInterfaceService.update(deptId, interfaceIds);
    }

    @ApiOperation("根据接口id删除 部门接口关联")
    @DeleteMapping("deleteByinterface")
    public ResultVo deleteDeptInterfaceByinterface(@ApiParam(value = "部门id", required = true) @RequestBody List<Long> interfaceIds) {
        if (Objects.isNull(interfaceIds) ) {
            return ResultVo.fail("interfaceId不能为空！");
        }
        return deptInterfaceService.deleteByinterface(interfaceIds);
    }
}

