package com.botong.oauth.service;

import com.botong.entity.BtoAuthUser;
import com.botong.entity.system.User;
import com.botong.oauth.manager.UserManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Service
public class BtoUserDetailServiceImpl implements UserDetailsService {

    @Autowired
    private UserManager userManager;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        com.botong.oauth.entity.UserDetails user = userManager.findByName(username);
        if (user != null) {
            String permissions = userManager.findUserPermissions(user.getUsername());
            boolean notLocked = false;
            if (StringUtils.equals(User.STATUS_VALID, user.getStatus())) {
                notLocked = true;
            }
            BtoAuthUser authUser = new BtoAuthUser(user.getUsername(), user.getPassword(), true, true, true, notLocked,
                    AuthorityUtils.commaSeparatedStringToAuthorityList(permissions));
            BeanUtils.copyProperties(user, authUser);
            return authUser;
        } else {
            throw new UsernameNotFoundException("");
        }
    }
}