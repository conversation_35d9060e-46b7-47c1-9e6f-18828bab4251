package com.botong.entity.router;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * Vue路由 Meta
 *
 * <AUTHOR> by zhb on 2023/7/19.
 */
@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RouterMeta implements Serializable {
    private static final long serialVersionUID = 3065968910910774008L;
    private String title;
    private String elIcon;
    private Boolean breadcrumb = true;
    private Boolean hidden = false;

}
