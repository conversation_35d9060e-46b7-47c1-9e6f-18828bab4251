package com.botong.serversystem.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.system.Dept;
import com.botong.entity.system.Tree;
import com.botong.serversystem.entity.respones.DeptInfoVO;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/17.
 */
public interface DeptService extends IService<Dept> {

    /**
     * 获取部门列表
     *
     * @param deptName 查询参数
     * @return 部门列表
     */
    List<? extends Tree<?>> selectList(String deptName);

    /**
     * 根据主键获取部门详情
     *
     * @param deptId 主键
     * @return 部门实体
     */
    Dept info(Long deptId);

    /**
     * 创建部门
     *
     * @param dept dept
     */
    void createDept(Dept dept);

    /**
     * 更新部门
     *
     * @param dept dept
     */
    void updateDept(Dept dept);

    /**
     * 删除部门
     *
     * @param deptIds 部门id集合
     */
    void deleteById(List<Long> deptIds);

    /**
     * 根据部门名称获取部门信息
     *
     * @param name 部门名称
     * @return 实体
     */
    Dept getDeptByName(String name);

    /**
     * 根据接口id获取部门信息
     *
     * @param interfaceId 接口id
     * @return {@link List }<{@link DeptInfoVO }>
     * <AUTHOR>
     * @since 2023-08-04 16:57:45
     */
    List<DeptInfoVO> getDeptByInterfaceId(Long interfaceId);
}
