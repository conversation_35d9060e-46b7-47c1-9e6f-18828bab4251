package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.serverinterface.entity.InterfaceClassification;
import com.botong.entity.vo.ResultVo;
import com.botong.serversystem.entity.respones.ClassInterfaceTree;
import com.botong.serversystem.entity.respones.ClassificationVO;

import java.util.List;


/**
 * (InterfaceClassification)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-05 09:24:55
 */
public interface InterfaceClassificationService extends IService<InterfaceClassification> {

    /**
     * 类别接口树
     *
     * @param classQueryDTO 类查询dto
     * @return {@link ResultVo }<{@link List }<{@link ClassInterfaceTree }>>
     * <AUTHOR>
     * @since 2023-08-05 20:51:13
     */
    ResultVo<List<ClassInterfaceTree>> classInterfaceTree(ClassificationVO classQueryDTO);

    /**
     * 给类别更新接口(增删改)
     *
     * @param classId 类别id
     * @param interfaceIds 接口id集合
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-05 20:50:05
     */
    ResultVo interfaceClass(Long classId, List<Long> interfaceIds);
    /**
     * 根据classid获取接口id集合
     *
     * @param classId 类别id
     * @return {@link ResultVo }<{@link List }<{@link Long }>>
     * <AUTHOR>
     * @since 2023-08-05 20:51:13
     */
    ResultVo<List<Long>> listByClass(Long classId);

    /**
     * 按接口删除
     *
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-07 14:20:34
     */
    ResultVo deleteByinterface(List<Long> interfaceIds);
}

