package com.botong.serverinterface.properties;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:server-interface.properties"})
@ConfigurationProperties(prefix = "server.interface")
public class BtoServerInterfaceProperties {

    /**
     * 免认证 URI，多个值的话以逗号分隔
     */
    private String anonUrl;

    private SwaggerProperties swagger = new SwaggerProperties();
}