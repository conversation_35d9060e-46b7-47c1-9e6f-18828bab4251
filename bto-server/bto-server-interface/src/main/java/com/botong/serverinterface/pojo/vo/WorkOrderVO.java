package com.botong.serverinterface.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2024/4/29.
 */
@Data
public class WorkOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    public final static String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 工单状态
     */
    private Integer status;

    /**
     * 电站id
     */
    // private String plantId;

    /**
     * 进件编号
     */
    private String orderId;

    /**
     * 电站类型
     */
    // private Integer plantType;

    /**
     * 电站名称
     */
    private String plantName;

    /**
     * 工单来源
     */
    private Integer source;

    /**
     * 故障时间
     */
    private String alarmTime;

    /**
     * 故障等级
     */
    private Integer alarmLevel;

    /**
     * 故障类型
     */
    private Integer alarmType;

    /**
     * 故障报修照片路径
     */
    private String issuePhoto;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;




    /**
     * 未修复原因
     */
    private String unrepairedCause;

    /**
     * 计划修复时间
     */
    private String preRepairTime;

    /**
     * 修复时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date repairTime;

    /**
     * 超时时间
     */
    // private String overtime;

    /**
     * 故障原因
     */
    private Integer alarmCause;

    /**
     * 维修详情
     */
    private String opCause;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 维修后照片
     */
    private String repairPhotoPath;

    /**
     * 故障现场照片
     */
    private String faultPhotoPath;

    /**
     * 维修状态 0：已修复 1 ： 未修复
     */
    private Integer repairStatus;


    /**
     * 故障现象
     */
    private Integer alarmInfo;


    /**
     * 故障设备
     */
    private Integer alarmDevice;

}


