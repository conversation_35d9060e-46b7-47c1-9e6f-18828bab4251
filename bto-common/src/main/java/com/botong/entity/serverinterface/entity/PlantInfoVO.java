package com.botong.entity.serverinterface.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
/**
 * (YuexiuPlantInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 15:31:46
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bp_v_plant_info")
public class PlantInfoVO {

    private String orderId;

    private String plantUid;
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

    private String powerDistributor;
    
    private String plantName;
    
    private String province;
    
    private String city;
    
    private String area;
    
    private String address;
    
    private String plantCapacity;
    
    private String power;
    
    private String todayElectricity;
    
    private String monthElectricity;
    
    private String yearElectricity;
    
    private String totalElectricity;
    
    private String inverterSn;

    @JsonIgnore
    private Integer special;
}

