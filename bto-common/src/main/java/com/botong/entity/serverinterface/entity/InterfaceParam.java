package com.botong.entity.serverinterface.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.botong.enums.RequiredEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * (InterfaceParam)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-13 10:41:35
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_interface_param")
public class InterfaceParam  {
    @TableId
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("参数名")
    private String paramName;
    @ApiModelProperty("描述")
    @Size(max = 85, message = "描述过长，最大长度为85")
    private String paramDescription;
    @ApiModelProperty("参数值")
    private String paramValue;
    @ApiModelProperty("参数类型 eg:String、integer、float、double")
    private String paramType;
    @ApiModelProperty("是否必须 1：是,2:否")
    @JsonFormat
    private RequiredEnum required;
    @ApiModelProperty("请求类型 query/body")
    private String requestType;

}

