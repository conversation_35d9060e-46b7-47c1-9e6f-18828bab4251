package com.botong.serverinterface.controller;

import com.botong.serverinterface.feign.PhotovoltaicFeign;
import com.botong.serverinterface.pojo.dto.InverterAlarmDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
public class TestController {

    @Autowired
    private PhotovoltaicFeign photovoltaicFeign;

    @GetMapping("test1")
    @PreAuthorize("hasAnyAuthority('user:add')")
    public String test1() {
        return "拥有'user:add'权限";
    }

    @GetMapping("test2")
    @PreAuthorize("hasAnyAuthority('user:update')")
    public String test2() {
        return "拥有'user:update'权限";
    }

    @GetMapping("user")
    public Principal currentUser(Principal principal) {
        return principal;
    }

    @PostMapping("test3")
    public Object test3(@RequestBody InverterAlarmDTO inverterAlarmDTO) {
        return this.photovoltaicFeign.getInverterAlarm(inverterAlarmDTO);
    }


}