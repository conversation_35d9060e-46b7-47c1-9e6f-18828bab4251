package com.botong.handler;

import com.botong.constant.BtoConstant;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.utils.ResponseUtil;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class BtoAuthExceptionEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
        ResponseUtil.makeResponse(response, BtoConstant.APPLICATION_JSON_UTF8_VALUE, HttpServletResponse.SC_UNAUTHORIZED, ResultVo.fail(ResultCode.INVALID_TOKEN)
        );
    }
}