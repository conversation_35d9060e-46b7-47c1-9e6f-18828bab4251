package com.botong.enums;

import lombok.Data;

/**
 * 排序字段枚举类
 * <AUTHOR>
 * @date 2023/7/17 11:28
 */

public enum SortTpyEnum {
    /**
     * 创建时间
     */
    SORT_CREATE_TIME("create_time"),
    /**
     * 更新时间
     */
    SORT_MODIFY_TIME("modify_time"),
    /**
     * 更新时间
     */
    SORT_UPDATE_TIME("update_time");
    /**
     * 排序字段名
     */
    private String name;

    /**
     * 构造方法
     */
    SortTpyEnum(String name) {
        this.name = name;
    }

    /**
     * 获取字段名
     * @return
     */
    public String getName() {
        return name;
    }
}
