package com.botong.serversystem.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.botong.constant.UserRoleConstant;
import com.botong.entity.CommonPage;
import com.botong.entity.vo.ResultVo;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.requerst.AddClassDTO;
import com.botong.serversystem.entity.requerst.ClassQueryDTO;
import com.botong.serversystem.entity.respones.ClassificationVO;
import com.botong.serversystem.service.ClassificationService;
import com.botong.utils.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @since 2023/7/26 9:58
 */
@Api(tags = "类别管理")
@RestController
@RequestMapping("/class")
public class ClassificationController {
    @Autowired
    private ClassificationService classificationService;

    @ApiOperation("类别列表查询")
    @GetMapping("/listAll")
    public ResultVo<List<ClassificationVO>> listAll(ClassQueryDTO classQueryDTO){
        Long deptId = Convert.toLong(JwtUtil.getCurrentUserInfo().get("deptId"));
        Long roleId = Convert.toLong(JwtUtil.getCurrentUserInfo().get("roleId"));
        if (Optional.ofNullable(deptId).map(id -> id == 0L).orElse(false) &&!UserRoleConstant.ADMIN_ROLE_ID.equals(roleId)) {
            throw new BtoException("请先绑定部门！");
        }
        if (UserRoleConstant.ADMIN_ROLE_ID.equals(roleId)){
            deptId = null;
        }
        List<ClassificationVO> list = classificationService.list(classQueryDTO,deptId);
        return ResultVo.success(list);
    }

    @ApiOperation("类别页")
    @GetMapping("/page")
    public ResultVo<CommonPage<ClassificationVO>> page(ClassQueryDTO classQueryDTO){
        CommonPage<ClassificationVO> page = classificationService.selectAll(classQueryDTO);
        return ResultVo.success(page);
    }



    @ApiOperation("更新类别")
    @PutMapping
    public ResultVo update(@RequestBody ClassificationVO updateClassDTO){
        if (ObjectUtil.isNull(updateClassDTO)) {
            throw new BtoException("参数不能为空");
        }
        return classificationService.updateClass(updateClassDTO);
    }

    @ApiOperation("添加类别")
    @PostMapping
    public ResultVo save(@RequestBody AddClassDTO addClassDTO){
        if (ObjectUtil.isNull(addClassDTO)) {
            throw new BtoException("参数不能为空");
        }
        return classificationService.addClass(addClassDTO);
    }

    @ApiOperation("删除类别")
    @DeleteMapping
    public ResultVo delete(@ApiParam(name = "类别id集合" ,required = true) @RequestBody List<Long> classIds){
        if (ObjectUtil.isNull(classIds)) {
            throw new BtoException("id不能为空");
        }
        return classificationService.deleteClassById(classIds);
    }

    @ApiOperation("根据接口id获取类别")
    @GetMapping("/list/{interfaceId}")
    public ResultVo<List<ClassificationVO>> listByInterfaceId(@ApiParam(value = "接口id" ,required = true)@PathVariable Long interfaceId){

        List<ClassificationVO> list = classificationService.listByInterfaceId(interfaceId);
        return ResultVo.success(list);
    }
    @ApiOperation("根据类别id获取类别信息")
    @GetMapping("/{classId}")
    public ResultVo<ClassificationVO> selectOne(@ApiParam(value = "类别id") @PathVariable Long classId){
       if (ObjectUtil.isNull(classId)) {
           throw new BtoException("id不能为空");
       }
        ClassificationVO classification = classificationService.selectOne(classId);
        return ResultVo.success(classification);
    }

}
