package com.botong.serversystem;

import com.botong.annotation.BtoCloudApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@SpringBootApplication
@EnableGlobalMethodSecurity(prePostEnabled = true)
@BtoCloudApplication
@EnableFeignClients("com.botong.api.module.**")
@ComponentScan({"com.botong.**"})
@MapperScan("com.botong.serversystem.mapper")
public class BtoServerSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(BtoServerSystemApplication.class, args);
    }

}
