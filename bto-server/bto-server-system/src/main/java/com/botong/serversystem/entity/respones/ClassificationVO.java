package com.botong.serversystem.entity.respones;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassificationVO {
    @NotNull(message = "id不能为空")
    @ApiModelProperty("类别id")
    private Long classId;
    @NotEmpty(message = "类别名不能为空")
    @ApiModelProperty("类别名")
    private String className;
}
