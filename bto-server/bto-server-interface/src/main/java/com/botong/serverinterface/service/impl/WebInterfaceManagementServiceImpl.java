package com.botong.serverinterface.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.api.module.serversystem.SystemApi;
import com.botong.entity.CommonPage;
import com.botong.entity.dto.SortType;
import com.botong.entity.serverinterface.dto.RoleQueryDTO;
import com.botong.entity.serverinterface.entity.InterfaceParam;
import com.botong.entity.serverinterface.entity.InterfaceRole;
import com.botong.entity.serverinterface.entity.WebInterfaceManagement;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import com.botong.entity.serverinterface.vo.RoleNameVO;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.RequestMethodEnum;
import com.botong.enums.ResultCode;
import com.botong.enums.StatusEnum;
import com.botong.exception.BtoException;
import com.botong.serverinterface.mapper.InterfaceRoleMapper;
import com.botong.serverinterface.mapper.WebInterfaceManagementMapper;
import com.botong.serverinterface.pojo.dto.*;
import com.botong.serverinterface.pojo.vo.InterfaceTree;
import com.botong.serverinterface.pojo.vo.InterfaceVO;
import com.botong.serverinterface.pojo.vo.RoleInterfaceTree;
import com.botong.serverinterface.service.InterfaceParamService;
import com.botong.serverinterface.service.InterfaceRoleService;
import com.botong.serverinterface.service.WebInterfaceManagementService;
import com.botong.utils.BeanCopyUtils;
import com.botong.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * web接口管理表(WebInterfaceManagement)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-12 14:15:08
 */
@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service("webInterfaceManagementService")
public class WebInterfaceManagementServiceImpl extends ServiceImpl<WebInterfaceManagementMapper, WebInterfaceManagement> implements WebInterfaceManagementService {

    @Autowired
    private WebInterfaceManagementService webInterfaceManagementService;
    @Autowired
    private InterfaceRoleService interfaceRoleService;
    @Autowired
    private SystemApi systemApi;
    @Autowired
    private InterfaceRoleMapper interfaceRoleMapper;
    @Autowired
    private InterfaceParamService interfaceParamService;


    @Override
    public CommonPage<InterfaceVO> queryInterfaceDataInPages(InterfaceQueryDTO interfaceQueryDTO,
                                                             Integer currentPage,
                                                             Integer size,
                                                             SortType sortType, Long deptId, Long classId) {
        QueryWrapper<WebInterfaceManagement> queryWrapper = new QueryWrapper<>();
        // List<Long> interfaceIdList = null;
        List<Long> interfaceIdList = systemApi.list(deptId).getData();
        if (ObjectUtil.isNotNull(classId)) {
            List<Long> interfaceIds = systemApi.listByClass(classId).getData();
            interfaceIdList.retainAll(interfaceIds);
        }
        // 如果接口id列表为空，直接返回空页
        if (CollUtil.isEmpty(interfaceIdList)) {
            return CommonPage.pageInfo(new ArrayList<>());
        }
        // 模糊查询
        if ((ObjectUtil.isNotNull(interfaceQueryDTO))) {
            queryWrapper.lambda()
                    .in(CollUtil.isNotEmpty(interfaceIdList), WebInterfaceManagement::getInterfaceId, interfaceIdList)
                    .like(StrUtil.isNotEmpty(interfaceQueryDTO.getName()), WebInterfaceManagement::getName, interfaceQueryDTO.getName())
                    .like(StrUtil.isNotEmpty(interfaceQueryDTO.getUrl()), WebInterfaceManagement::getUrl, interfaceQueryDTO.getUrl())
                    // 处理requestMethod为null
                    .like(ObjectUtil.isNotNull(interfaceQueryDTO.getRequestMethod()), WebInterfaceManagement::getRequestMethod, Optional.ofNullable(interfaceQueryDTO.getRequestMethod()).map(RequestMethodEnum::getName).orElse(""))
                    .like(StrUtil.isNotEmpty(interfaceQueryDTO.getRequestHeader()), WebInterfaceManagement::getRequestHeader, interfaceQueryDTO.getRequestHeader())
                    .like(StrUtil.isNotEmpty(interfaceQueryDTO.getInterfaceDescribe()), WebInterfaceManagement::getInterfaceDescribe, interfaceQueryDTO.getInterfaceDescribe())
                    // 处理Status为null
                    .like(ObjectUtil.isNotNull(interfaceQueryDTO.getStatus()), WebInterfaceManagement::getStatus, Optional.ofNullable(interfaceQueryDTO.getStatus()).map(StatusEnum::getName).orElse(""))
                    .like(StrUtil.isNotEmpty(interfaceQueryDTO.getResponseHeader()), WebInterfaceManagement::getResponseHeader, interfaceQueryDTO.getResponseHeader());

        }
        // 排序
        if (ObjectUtil.isNotNull(sortType)) {
            queryWrapper.orderBy(true, sortType.getIsAsc(), sortType.getSortField());
        } else {
            queryWrapper.lambda().orderByDesc(WebInterfaceManagement::getCreateTime);
        }
        // 分页查询
        Page<WebInterfaceManagement> page = new Page<>(currentPage, size);
        Page<WebInterfaceManagement> interfacePage = this.page(page, queryWrapper);
        List<WebInterfaceManagement> interfaceList = interfacePage.getRecords();

        // 获取interfaceidList
        List<Long> interfaceIds = interfaceList.stream().map(WebInterfaceManagement::getInterfaceId).collect(Collectors.toList());
        // 根据interface_id筛选出paramList
        LambdaQueryWrapper<InterfaceParam> paramQueryWrapper = new LambdaQueryWrapper<>();
        paramQueryWrapper.in((CollUtil.isNotEmpty(interfaceIds)), InterfaceParam::getInterfaceId, interfaceIds);
        List<InterfaceParam> paramList = interfaceParamService.list(paramQueryWrapper);

        // 转换实体类
        List<InterfaceParamDTO> paramDTOList = BeanCopyUtils.copyBeanList(paramList, InterfaceParamDTO.class);
        List<InterfaceVO> interfaceVOList = BeanCopyUtils.copyBeanList(interfaceList, InterfaceVO.class);

        for (InterfaceVO interfaceVO : interfaceVOList) {
            Long interfaceId = interfaceVO.getInterfaceId();
            // 根据interfaceid把paramList插入对应的接口
            List<InterfaceParamDTO> requestParams = paramDTOList.stream()
                    .filter(param -> interfaceId.equals(param.getInterfaceId()))
                    .collect(Collectors.toList());
            interfaceVO.setRequestParas(requestParams);
        }

        return CommonPage.pageInfo(interfacePage, interfaceVOList);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultVo addInterface(AddInterfaceDTO interfaceDTO) {
        WebInterfaceManagement webInterfaceManagement = BeanCopyUtils.copyBean(interfaceDTO, WebInterfaceManagement.class);

        boolean success = webInterfaceManagementService.save(webInterfaceManagement);
        Long deptId = Convert.toLong(JwtUtil.getCurrentUserInfo().get("deptId"));
        List<Long> interfaceIds = Arrays.asList(webInterfaceManagement.getInterfaceId());
        systemApi.insert(deptId, interfaceIds);

        // 保存InterfaceParam表
        if (ObjectUtil.isNotNull(interfaceDTO.getRequestParas())) {
            List<ModifyParamDTO> requestParas = interfaceDTO.getRequestParas();
            List<InterfaceParam> paramList = BeanCopyUtils.copyBeanList(requestParas, InterfaceParam.class);
            paramList.forEach(para -> para.setInterfaceId(webInterfaceManagement.getInterfaceId()));
            interfaceParamService.saveBatch(paramList);
        }
        if (success) {
            return ResultVo.success(ResultCode.ADD_SUCCESS);
        } else {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultVo updateByInterface(InterfaceDTO interfaceDTO) {
        WebInterfaceManagement webInterfaceManagement = BeanCopyUtils.copyBean(interfaceDTO, WebInterfaceManagement.class);
        boolean success = webInterfaceManagementService.updateById(webInterfaceManagement);
        // 删除旧的参数数据
        Long interfaceId = interfaceDTO.getInterfaceId();
        interfaceParamService.lambdaUpdate().eq(InterfaceParam::getInterfaceId, interfaceId).remove();
        // 写入新的参数数据
        if (ObjectUtil.isNotNull(interfaceDTO.getRequestParas())) {
            List<ModifyParamDTO> requestParas = interfaceDTO.getRequestParas();
            List<InterfaceParam> paramList = BeanCopyUtils.copyBeanList(requestParas, InterfaceParam.class);
            paramList.forEach(para -> para.setInterfaceId(interfaceId));
            interfaceParamService.saveBatch(paramList);
        }
        if (success) {
            return ResultVo.success(ResultCode.EDIT_SUCCESS);
        } else {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultVo deleteByInterface(List<Long> ids) {
        boolean success = webInterfaceManagementService.removeByIds(ids);
        // 删除旧数据
        interfaceParamService.lambdaUpdate().in(InterfaceParam::getInterfaceId, ids).remove();
        interfaceRoleService.lambdaUpdate().in(InterfaceRole::getInterfaceId, ids).remove();
        systemApi.deleteDeptInterfaceByinterface(ids);
        systemApi.deleteInterfaceClassByinterface(ids);
        if (success) {
            return ResultVo.success(ResultCode.DELETE_SUCCESS);
        } else {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Override
    public ResultVo changeStatusById(InterfaceChangStatusDTO interfaceChangStatusDTO) {
        WebInterfaceManagement webInterfaceManagement = BeanCopyUtils.copyBean(interfaceChangStatusDTO, WebInterfaceManagement.class);
        boolean success = webInterfaceManagementService.updateById(webInterfaceManagement);
        if (success) {
            return ResultVo.success(ResultCode.EDIT_SUCCESS);
        } else {
            throw new BtoException(ResultCode.FAIL);
        }
    }

    @Override
    public ResultVo addInterfaceToRole(Long roleId, List<Long> interfaceIds) {
        ArrayList<InterfaceRole> interfaceRoles = new ArrayList<>();
        for (Long id : interfaceIds) {
            InterfaceRole interfaceRole = new InterfaceRole(id, roleId);
            interfaceRoles.add(interfaceRole);
        }
        interfaceRoleService.lambdaUpdate().eq(InterfaceRole::getRoleId, roleId).remove();
        boolean success = interfaceRoleService.saveBatch(interfaceRoles);
        if (success) {
            return ResultVo.success();
        }
        return ResultVo.fail();
    }


    @Override
    public ResultVo<List<RoleInterfaceTree>> roleInterfaceTree(RoleQueryDTO roleQueryDTO) {
        List<RoleNameVO> roleNameList = systemApi.getRoleNameList(roleQueryDTO).getData();
        List<Long> roleIds = roleNameList.stream().map(RoleNameVO::getRoleId).collect(Collectors.toList());
        if (CollUtil.isEmpty(roleNameList)) {
            throw new BtoException("角色id不能为空");
        }

        List<InterfaceTree> interfaceTreeList = interfaceRoleMapper.getInterfaceTree(roleIds);
        List<RoleInterfaceTree> roleInterfaceTree = BeanCopyUtils.copyBeanList(roleNameList, RoleInterfaceTree.class);
        if (CollUtil.isEmpty(interfaceTreeList)) {
            return ResultVo.success(roleInterfaceTree);
        }

        for (RoleInterfaceTree tree : roleInterfaceTree) {
            Long roleId = tree.getRoleId();
            List<InterfaceTree> treeList = interfaceTreeList.stream()
                    .filter(interfaceTree -> roleId.equals(interfaceTree.getRoleId()))
                    .collect(Collectors.toList());
            tree.setInterfaceTreeList(treeList);
        }
        roleInterfaceTree.toString();
        return ResultVo.success(roleInterfaceTree);
    }

    @Override
    public ResultVo<List<InterfaceNameVO>> getInterfaceName() {
        List<InterfaceNameVO> interfaceName = baseMapper.getInterfaceName();

        Set<InterfaceNameVO> interfaceNameSet = new HashSet<>(interfaceName);
        List<InterfaceNameVO>  newList = new ArrayList<>(interfaceNameSet);
        return ResultVo.success(newList);
    }
    @Override
    public ResultVo<List<InterfaceNameVO>> getInterfaceInfoRepeat() {
        List<InterfaceNameVO> interfaceName = baseMapper.getInterfaceName();
        return ResultVo.success(interfaceName);
    }

}

