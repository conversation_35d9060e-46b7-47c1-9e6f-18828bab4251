package com.botong.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Data
@TableName("t_menu")
@ApiModel
public class Menu implements Serializable {

    private static final long serialVersionUID = 7187628714679791771L;

    /**
     * 菜单
     */
    public static final String TYPE_MENU = "0";
    /**
     * 按钮
     */
    public static final String TYPE_BUTTON = "1";

    /**
     * 菜单/按钮ID
     */
    @TableId(value = "menu_id", type = IdType.AUTO)
    @ApiModelProperty(value = "菜单/按钮ID")
    private Long menuId;

    /**
     * 上级菜单ID
     */
    @TableField("parent_id")
    @ApiModelProperty(value = "上级菜单ID", required = true)
    private Long parentId;

    /**
     * 菜单/按钮名称
     */
    @TableField("menu_name")
    @ApiModelProperty(value = "菜单/按钮名称", required = true)
    private String menuName;

    /**
     * 标题
     */
    @TableField("title")
    @ApiModelProperty(value = "标题", required = true)
    private String title;

    /**
     * 菜单URL
     */
    @TableField("path")
    @ApiModelProperty(value = "菜单URL")
    private String path;

    /**
     * 对应 Vue组件
     */
    @TableField("component")
    @ApiModelProperty(value = "Vue组件")
    private String component;

    /**
     * 权限标识
     */
    @TableField("perms")
    @ApiModelProperty(value = "权限标识")
    private String perms;

    /**
     * 图标
     */
    @TableField("icon")
    @ApiModelProperty(value = "图标")
    private String icon;

    /**
     * 重定向
     */
    @TableField("redirect")
    @ApiModelProperty(value = "重定向")
    private String redirect;

    /**
     * 类型 0菜单 1按钮
     */
    @TableField("type")
    @ApiModelProperty(value = "类型 0菜单 1按钮", required = true)
    private String type;

    /**
     * 排序
     */
    @TableField("order_num")
    @ApiModelProperty(value = "排序")
    private Integer orderNum;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间", hidden = true)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间", hidden = true)
    private LocalDateTime modifyTime;

}