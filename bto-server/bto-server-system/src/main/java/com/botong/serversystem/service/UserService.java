package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.system.User;
import com.botong.entity.vo.ResultVo;
import com.botong.serversystem.entity.requerst.UserQueryDTO;
import com.botong.serversystem.entity.respones.SystemUserVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public interface UserService extends IService<User> {

    /**
     * 通过用户名查找用户是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    Boolean findUserIsExists(String username);

    /**
     * 获取用户列表
     *
     * @param userQueryDTO 查询参数
     * @return 用户集合
     */
    PageInfo<SystemUserVo> getList(UserQueryDTO userQueryDTO);

    /**
     * 根据主键获取用户详情
     *
     * @param userId 主键
     * @return 用户实体
     */
    SystemUserVo info(Long userId);

    /**
     * 新增用户
     *
     * @param user 实体
     * @return 响应实体
     */
    ResultVo<Object> add(SystemUserVo user);

    /**
     * 根据主键修改信息
     *
     * @param user 实体
     * @return 响应实体
     */
    ResultVo<Object> update(SystemUserVo user);

    /**
     * 根据主键删除用户信息
     *
     * @param userIds 用户id集合
     * @return 响应实体
     */
    ResultVo<Object> delete(List<Long> userIds);

    /**
     * 更新用户密码
     *
     * @param password 新密码
     */
    void updatePassword(String password);

    /**
     * 重置密码
     *
     * @param userIds 用户id集合
     */
    void resetPassword(List<Long> userIds);
}

