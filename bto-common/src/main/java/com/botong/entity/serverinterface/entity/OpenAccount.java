package com.botong.entity.serverinterface.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */

@TableName("t_open_account")
@Data
public class OpenAccount {

    /**
     * redis key前缀
     */
    public static final String REDIS_PREFIX = "bto.open.";

    @TableId(value = "id", type = IdType.AUTO)
    private String id;
    private String grantType;
    private String clientId;
    private String clientSecret;
    private String scope;
    private String remark;
    private String groupType;
}