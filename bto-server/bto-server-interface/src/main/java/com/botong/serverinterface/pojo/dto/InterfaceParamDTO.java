package com.botong.serverinterface.pojo.dto;

import cn.hutool.core.util.ObjectUtil;
import com.botong.enums.RequiredEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2023/7/13 17:29
 */
@Data
@ToString
public class InterfaceParamDTO {
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("参数名")
    private String paramName;
    @ApiModelProperty("描述")
    @Size(max = 85, message = "描述过长，最大长度为85")
    private String paramDescription;
    @ApiModelProperty("参数值")
    private String paramValue;
    @ApiModelProperty("参数类型 eg:String、integer、float、double")
    private String paramType;
    @ApiModelProperty("是否必须 1：是,2:否")
    @JsonFormat
    private RequiredEnum required;
    @ApiModelProperty("请求类型 query/body")
    private String requestType;

    public String getRequired() {
        return ObjectUtil.isNotNull(required)? required.getName() : "UNKNOWN";
    }
}
