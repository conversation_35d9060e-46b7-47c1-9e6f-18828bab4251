package com.botong.serverinterface.feign;

import com.botong.constant.BtoInterfaceUrl;
import com.botong.serverinterface.configure.FeignConfigure;
import com.botong.serverinterface.fallback.PhotovoltaicFeignFactory;
import com.botong.serverinterface.pojo.dto.InverterAlarmDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */

@Component
@FeignClient(name = "photovoltaic", url = BtoInterfaceUrl.PHOTOVOLTAIC_PREFIX_URL, fallback = PhotovoltaicFeignFactory.class, configuration = FeignConfigure.class)
public interface PhotovoltaicFeign {

    @PostMapping(value = BtoInterfaceUrl.INVERTER_ALARM_URL)
    Object getInverterAlarm(@RequestBody InverterAlarmDTO inverterAlarmDTO);


    @GetMapping(value = BtoInterfaceUrl.INVERTER_ALARM_DICT_URL)
    Object getInverterAlarmDict(@RequestParam ArrayList<String> manufacturers);
}
