<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.botong</groupId>
        <artifactId>bto-server</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.botong.api</groupId>
    <artifactId>bto-server-api</artifactId>
    <packaging>jar</packaging>

    <name>bto-server-api</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 公共依赖 -->
        <dependency>
            <groupId>com.botong</groupId>
            <artifactId>bto-common</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

</project>
