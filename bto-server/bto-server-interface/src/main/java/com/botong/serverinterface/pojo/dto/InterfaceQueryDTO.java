package com.botong.serverinterface.pojo.dto;

import com.botong.enums.RequestMethodEnum;
import com.botong.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2023/7/13 11:47
 */
@Data
@AllArgsConstructor
public class InterfaceQueryDTO {
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("接口描述")
    @Size(max = 85, message = "描述过长，最大长度为85")
    private String interfaceDescribe;
    @ApiModelProperty("接口请求方式，1:post,2:get,3:put,4:delete")
    @JsonFormat
    private RequestMethodEnum requestMethod;
    @ApiModelProperty("接口url")
    private String url;
    @ApiModelProperty("请求头")
    private String requestHeader;
    @ApiModelProperty("响应头")
    private String responseHeader;
    @ApiModelProperty("接口状态 0:失效，1:正常")
    @JsonFormat
    private StatusEnum status;

}
