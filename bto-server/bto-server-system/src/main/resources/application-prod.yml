spring:
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 5000
    password: botongRedis66
  boot:
    admin:
      client:
        url: http://localhost:8401
        username: bto
        password: btoadmin
  datasource:
    dynamic:
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: BtoHikariCP
      primary: base
      datasource:
        base:
          username: admin
          password: v25MViJ6SGLEEEax
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848