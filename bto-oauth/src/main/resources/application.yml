server:
  port: 8101

spring:
  profiles:
   active: prod
  application:
    name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

mybat<PERSON>-plus:
  #实体类配置别名，默认为类名首字母小写
  type-aliases-package: com.botong.entity
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: null
  global-config:
    banner: false

info:
  app:
    name: ${spring.application.name}
    description: "@project.description@"
    version: "@project.version@"
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS