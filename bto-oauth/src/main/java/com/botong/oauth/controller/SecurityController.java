package com.botong.oauth.controller;

import com.botong.entity.vo.ResultVo;
import com.botong.exception.BtoAuthException;
import com.botong.exception.ValidateCodeException;
import com.botong.oauth.service.ValidateCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.Principal;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
@Slf4j
@Api(tags = "权限管理")
public class SecurityController {

    @Autowired
    private ConsumerTokenServices consumerTokenServices;

    @Autowired
    private ValidateCodeService validateCodeService;

    @GetMapping("captcha")
    @ApiOperation(value = "获取验证码")
    public ResultVo<Object> getCaptcha(@ApiParam(required = true, value = "当前时间戳") String key,
                                       HttpServletResponse response,
                                       @ApiParam(required = true, value = "响应类型：1.picture 2.number") String responseType) throws IOException, ValidateCodeException {
        return validateCodeService.createVerificationCode(key, response, responseType);
    }

    @GetMapping("user")
    @ApiOperation(value = "获取当前用户信息")
    public ResultVo<Principal> currentUser(Principal principal) {
        return ResultVo.success(principal);
    }

    @PostMapping("logout")
    @ApiOperation(value = "退出")
    public ResultVo<Object> logout(HttpServletRequest request) throws BtoAuthException {
        String authorization = request.getHeader("Authorization");
        String token = StringUtils.replace(authorization, "bearer ", "");
        String message = "logout success";
        if (!consumerTokenServices.revokeToken(token)) {
            message = "logout failure";
            log.error(message);
            return ResultVo.fail(message);
        }
        return ResultVo.success(message);
    }
}