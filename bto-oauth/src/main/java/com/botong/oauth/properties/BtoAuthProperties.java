package com.botong.oauth.properties;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:bto-auth.properties"})
@ConfigurationProperties(prefix = "bto.auth")
public class BtoAuthProperties {

    private BtoClientsProperties[] clients = {};
    private int accessTokenValiditySeconds = 60 * 60 * 24;
    private int refreshTokenValiditySeconds = 60 * 60 * 24 * 7;

    /**
     * 免认证路径
     */
    private String anonUrl;

    private SwaggerProperties swagger = new SwaggerProperties();

    /**
     * 验证码配置类
     */
    private BtoValidateCodeProperties code = new BtoValidateCodeProperties();

}