package com.botong.oauth.aspect;

import com.botong.entity.router.VueRouter;
import com.botong.entity.system.Menu;
import com.botong.entity.vo.ResultVo;
import com.botong.oauth.service.MenuService;
import com.botong.utils.NullUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Component;

import java.security.Principal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/7/19.
 */
@Slf4j
@Component
@Aspect
public class OauthTokenAspect {

    @Autowired
    private MenuService menuService;

    @Autowired
    private ObjectMapper objectMapper;

    private final String OPEN_GRANT_TYPE = "client_credentials";


    @Around("execution(* org.springframework.security.oauth2.provider.endpoint.TokenEndpoint.postAccessToken(..))")
    public Object handleControllerMethod(ProceedingJoinPoint joinPoint) throws Throwable {


        Object[] args = joinPoint.getArgs();
        Principal principal = (Principal) args[0];
        Map arg = (Map) args[1];
        String grantType = arg.get("grant_type").toString();
        if (!(principal instanceof Authentication)) {
            throw new InsufficientAuthenticationException("There is no client authentication. Try adding an appropriate authentication filter.");
        }

        Object proceed = joinPoint.proceed();
        Map<String, Object> map = null;
        if (proceed != null) {
            ResponseEntity<OAuth2AccessToken> responseEntity = (ResponseEntity<OAuth2AccessToken>) proceed;
            OAuth2AccessToken body = responseEntity.getBody();
            if (NullUtils.isNotNull(grantType)) {
                if (grantType.equals(OPEN_GRANT_TYPE)) {
                    return ResponseEntity.status(200).body(ResultVo.success(body));
                }
            }
            assert body != null;
            Map<String, Object> additionalInformation = body.getAdditionalInformation();

            Object userInfo = additionalInformation.get("userInfo");
            Map entry = (Map) userInfo;
            String bodyString = objectMapper.writeValueAsString(body);
            map = objectMapper.readValue(bodyString, Map.class);
            if (entry != null) {
                Map<String, Object> userRouters = getUserRouters(entry.get("username").toString());
                map.put("userRouters", userRouters);
            }

        }
        return ResponseEntity.status(200).body(ResultVo.success(map));
    }

    public Map<String, Object> getUserRouters(String username) {
        Map<String, Object> result = new HashMap<>(16);
        List<VueRouter<Menu>> userRouters = this.menuService.getUserRouters(username);
        String userPermissions = this.menuService.findUserPermissions(username);
        String[] permissionArray = new String[0];
        if (StringUtils.isNoneBlank(userPermissions)) {
            permissionArray = StringUtils.splitByWholeSeparatorPreserveAllTokens(userPermissions, ",");
        }
        result.put("routes", userRouters);
        result.put("permissions", permissionArray);
        return result;
    }


}