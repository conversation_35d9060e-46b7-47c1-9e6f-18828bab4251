package com.botong.serversystem.controller;

import java.util.List;

import com.botong.entity.CommonPage;
import com.botong.entity.system.User;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.serversystem.entity.requerst.PasswordDTO;
import com.botong.serversystem.entity.requerst.UserQueryDTO;
import com.botong.serversystem.entity.respones.SystemUserVo;
import com.botong.serversystem.service.UserService;
import com.botong.utils.JwtUtil;
import com.botong.utils.NullUtils;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpoint;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@Validated
@RequestMapping("user")
@Api(tags = "用户管理")
public class UserController {

    private final UserService userService;

    private final PasswordEncoder passwordEncoder;

    /**
     * 获取用户列表
     *
     * @return 用户集合
     */
    @ApiOperation("获取用户列表")
    @PostMapping("/list")
    public ResultVo<CommonPage<SystemUserVo>> getList(@RequestBody UserQueryDTO userQueryDTO) {
        PageInfo<SystemUserVo> users = userService.getList(userQueryDTO);
        return ResultVo.success(CommonPage.pageInfo(users));
    }


    /**
     * 根据主键获取用户详情
     *
     * @param userId 主键
     * @return 用户实体
     */
    @ApiOperation("根据主键获取用户详情")
    @GetMapping("/info/{userId}")
    public ResultVo<SystemUserVo> info(@PathVariable("userId") Long userId) {
        boolean result = NullUtils.isNull(userId);
        if (result) {
            String message = "userId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            SystemUserVo systemUserVo = userService.info(userId);
            return ResultVo.success(systemUserVo);
        }
    }

    /**
     * 新增用户
     *
     * @param user 实体
     */
    @ApiOperation("新增用户")
    @PostMapping("/add")
    public ResultVo<Object> add(@RequestBody SystemUserVo user) {
        return userService.add(user);
    }


    /**
     * 根据主键修改信息
     *
     * @param user 实体
     */
    @ApiOperation("根据主键修改信息")
    @PostMapping("/update")
    public ResultVo<Object> update(@RequestBody SystemUserVo user) {
        boolean result = NullUtils.isNull(user.getUserId());
        if (result) {
            String message = "userId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            return userService.update(user);
        }
    }

    /**
     * 根据主键删除用户信息
     *
     * @param userIds 用户id集合
     */
    @ApiOperation("批量删除")
    @PostMapping("/delete")
    public ResultVo<Object> delete(@RequestBody List<Long> userIds) {
        boolean result = NullUtils.isNull(userIds);
        if (result) {
            String message = "userIds must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        }
        return userService.delete(userIds);
    }

    /**
     * 校验密码是否正确
     *
     * @param passwordDTO 原始密码、更新密码实体
     */
    @ApiOperation("用户更新密码")
    @PostMapping("/password")
    public ResultVo<Object> updatePassword(@RequestBody PasswordDTO passwordDTO) {
        Long currentUserId = JwtUtil.getCurrentUserId();
        User user = userService.getById(currentUserId);
        boolean result = user != null && passwordEncoder.matches(passwordDTO.getOriginalPassword(), user.getPassword());
        if (Boolean.FALSE.equals(result)) {
            return ResultVo.fail(ResultCode.INVALID_ORIGINAL_PASSWORD);
        } else {
            userService.updatePassword(passwordDTO.getUpdatePassword());
        }
        return ResultVo.success(ResultCode.EDIT_SUCCESS);
    }

    /**
     * 重置用户密码，可批量重置
     *
     * @param userIds 用户id集合
     */
    @ApiOperation("重置用户密码，可批量重置")
    @PostMapping("password/reset")
    public ResultVo<Object> resetPassword(@RequestBody List<Long> userIds) {
        boolean result = NullUtils.isNull(userIds);
        if (result) {
            String message = "userIds must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        }
        this.userService.resetPassword(userIds);
        return ResultVo.success(ResultCode.EDIT_SUCCESS);
    }

}
