
package com.botong.serverinterface.configure;

import com.botong.serverinterface.properties.BtoServerInterfaceProperties;
import com.botong.serverinterface.properties.SwaggerProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Configuration
@EnableSwagger2
@Import(BeanValidatorPluginsConfiguration.class)
public class SwaggerConfiguration {

    @Autowired
    private BtoServerInterfaceProperties properties;

    @Bean(value = "serverInterfaceApi")
    @Order(value = 1)
    public Docket groupRestApi() {
        SwaggerProperties swagger = properties.getSwagger();
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .title(swagger.getTitle())
                        .description("<div style='font-size:14px;color:red;'>" + swagger.getDescription() + "</div>")
                        .termsOfServiceUrl(swagger.getUrl())
//                        .contact(swagger.getEmail())
                        .version(swagger.getVersion())
                        .build())
                .select()
                .apis(RequestHandlerSelectors.basePackage(swagger.getBasePackage()))
                .paths(PathSelectors.any())
                .build();
    }


}
