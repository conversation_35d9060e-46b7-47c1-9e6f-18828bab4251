package com.botong.serverinterface.controller;


import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.InverterRtdataDetailRecord;
import com.botong.entity.vo.ResultVo;
import com.botong.serverinterface.service.InverterRtdataDetailRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.botong.constant.PlantInfoSpecialConstants.YUEXIU_SPECIAL;

/**
 * (InverterRtdataDetailRecord)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-14 16:08:50
 */
@Api(tags = "逆变器信息")
@RestController
@RequestMapping("inverterRtdataDetailRecord")
public class InverterRtdataDetailRecordController {
    /**
     * 服务对象
     */
    @Resource
    private InverterRtdataDetailRecordService inverterRtdataDetailRecordService;

    /**
     *
     * @param currentPage
     * @param size
     * @return
     */

    @ApiOperation("逆变器信息查询")
    @PostMapping("/page")
    public ResultVo<CommonPage<InverterRtdataDetailRecord>> queryInverterDetailRecordPages(
                     @RequestParam(required = false, defaultValue = "1", value = "currentPage") Integer currentPage,
                     @RequestParam(required = false, defaultValue = "10", value = "size") Integer size){
        CommonPage<InverterRtdataDetailRecord> pageVo = inverterRtdataDetailRecordService.queryInverterDetailRecordPages(currentPage, size,YUEXIU_SPECIAL);
        return ResultVo.success(pageVo);
    }
}

