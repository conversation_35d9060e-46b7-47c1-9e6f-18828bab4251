spring:
  redis:
    cluster:
      nodes: ***************:6379,***************:6379,***************:6379
    database: 0
    host: ***************
    port: 6379
    timeout: 5000
    password:
  boot:
    admin:
      client:
        url: http://localhost:8401
        username: bto
        password: btoadmin
  datasource:
    dynamic:
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: BtoHikariCP
      primary: base
      datasource:
        base:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: admin
          password: bto123
          url: *********************************************************************************************************************************************************************
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848