package com.botong.configure;

import com.botong.handler.BtoAccessDeniedHandler;
import com.botong.handler.BtoAuthExceptionEntryPoint;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class BtoAuthExceptionConfigure {

    @Bean
    @ConditionalOnMissingBean(name = "accessDeniedHandler")
    public BtoAccessDeniedHandler accessDeniedHandler() {
        return new BtoAccessDeniedHandler();
    }

    @Bean
    @ConditionalOnMissingBean(name = "authenticationEntryPoint")
    public BtoAuthExceptionEntryPoint authenticationEntryPoint() {
        return new BtoAuthExceptionEntryPoint();
    }
}