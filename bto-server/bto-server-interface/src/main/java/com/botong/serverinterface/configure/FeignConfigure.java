package com.botong.serverinterface.configure;

import com.botong.entity.serverinterface.entity.OpenAccount;
import com.botong.serverinterface.service.OpenAccountService;
import com.botong.service.RedisService;
import com.botong.utils.JwtUtil;
import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */
public class FeignConfigure implements RequestInterceptor {

    /**
     * oauth2 token请求头名称
     */
    public static final String AUTHORIZATION = "Authorization";

    @Resource
    private RedisService redisService;

    @Resource
    private OpenAccountService openAccountService;

    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String token = this.getToken(getCurrentUserName());
        requestTemplate.header(AUTHORIZATION, "bearer " + token);
    }

    public String getToken(String key) {
        Object token = redisService.get(OpenAccount.REDIS_PREFIX + key);
        if (Objects.isNull(token)) {
            openAccountService.getPhotovoltaicAccessTokenParameter();
            token = redisService.get(OpenAccount.REDIS_PREFIX + key);
        }
        return token.toString();
    }

    String getCurrentUserName() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails) authentication.getDetails();
        String tokenValue = details.getTokenValue();
        Map<String, Object> map = JwtUtil.analyzeToken(tokenValue);
        return map.get("username").toString();
    }

}