package com.botong.serverinterface.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.serverinterface.entity.InterfaceParam;
import com.botong.serverinterface.mapper.InterfaceParamMapper;
import com.botong.serverinterface.service.InterfaceParamService;
import org.springframework.stereotype.Service;

/**
 * (InterfaceParam)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-13 11:00:07
 */
@Service("interfaceParamService")
public class InterfaceParamServiceImpl extends ServiceImpl<InterfaceParamMapper, InterfaceParam> implements InterfaceParamService {

}

