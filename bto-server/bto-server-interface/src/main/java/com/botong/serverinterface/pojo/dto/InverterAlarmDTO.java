package com.botong.serverinterface.pojo.dto;

import com.botong.entity.dto.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class InverterAlarmDTO extends PageDTO {
    @ApiModelProperty(value = "查询告警类型: alarm:查询告警 selfCheck:查询自检提示", required = true)
    private String alarmType;
    @ApiModelProperty(value = "需要查询的电站编号")
    private String plantUid;
    @ApiModelProperty(value = "需要查询的电站名称(模糊查询)")
    private String plantName;
    @ApiModelProperty(value = "查询条件：开始时间", notes = "默认为当天")
    private String startTime;
    @ApiModelProperty(value = "查询条件：结束时间", notes = "默认为当天")
    private String endTime;
    @ApiModelProperty("筛选条件1：告警信息")
    private String alarmInfo;
    @ApiModelProperty("筛选条件2：事件描述")
    private String alarmMean;
    @ApiModelProperty("筛选条件3：级别")
    private String alarmLevel;
    @ApiModelProperty("筛选条件4：状态（0未处理，1已处理 , 2失效）")
    private String alarmStatus;
    @ApiModelProperty(value = "项目ID", hidden = true)
    private Integer special;
}
