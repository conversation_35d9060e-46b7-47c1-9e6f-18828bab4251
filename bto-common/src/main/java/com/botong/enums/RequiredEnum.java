package com.botong.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Getter
@Slf4j
public enum RequiredEnum {
    /**
     * 空字符串 “”
     */
    NULL("-2", ""),
    /**
     * 未知
     */
    UNKNOWN("-1", "UNKNOWN"),
    FALSE("0", "false"),
    TRUE("1", "true"),
    ;

    private final String code;
    @EnumValue
    private final String name;

    RequiredEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    @JsonCreator
    public static RequiredEnum fromValue(String value) {
        if (StrUtil.isEmpty(value)) {
            return RequiredEnum.NULL;
        }
        for (RequiredEnum required : RequiredEnum.values()) {
            if (StrUtil.equals(required.getCode(), value)) {
                return required;
            }
        }
        throw  new IllegalArgumentException("Invalid required value : "+ value);
    }
}