package com.botong.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */

@Getter
@Slf4j
public enum RequestMethodEnum {
    /**
     * 空字符串 “”
     */
    NULL("-2", ""),
    /**
     * 未知
     */
    UNKNOWN("-1", "UNKNOWN"),
    /**
     * POST
     */
    POST("1", "POST"),
    /**
     * GET
     */
    GET("2", "GET"),
    /**
     * PUT
     */
    PUT("3", "PUT"),
    /**
     * DELETE
     */
    DELETE("4", "DELETE"),
    ;

    private final String code;
    @EnumValue
    private final String name;

    RequestMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    @JsonCreator
    public static RequestMethodEnum fromValue(String value) {
        if (StrUtil.isEmpty(value)) {
            return RequestMethodEnum.NULL;
        }
        for (RequestMethodEnum requestMethodEnum : RequestMethodEnum.values()) {
            if (StrUtil.equals(requestMethodEnum.getCode(), value)) {
                return requestMethodEnum;
            }
        }
        throw  new IllegalArgumentException("Invalid RequestMethod value : "+ value);
    }
}