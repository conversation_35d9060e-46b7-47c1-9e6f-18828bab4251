<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.botong.serverinterface.mapper.InterfaceRoleMapper">
    <!--    List<InterfaceTree> getInterfaceTree();-->
    <select id="getInterfaceTree" resultType="com.botong.serverinterface.pojo.vo.InterfaceTree">
        SELECT t1.interface_id, t1.name, t2.role_id
        FROM t_interface_info t1
        LEFT JOIN t_interface_role t2 ON t1.interface_id = t2.interface_id
        <where>
            role_id in
            <foreach collection="roleIds" open="(" close=")" separator="," item="roleId">
                #{roleId}
            </foreach>
        </where>
    </select>
</mapper>