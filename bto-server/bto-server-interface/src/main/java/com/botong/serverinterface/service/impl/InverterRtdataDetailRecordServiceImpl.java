package com.botong.serverinterface.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.InverterRtdataDetailRecord;
import com.botong.serverinterface.mapper.InverterRtdataDetailRecordMapper;
import com.botong.serverinterface.pojo.dto.InverterQueryDTO;
import com.botong.serverinterface.service.InverterRtdataDetailRecordService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * (InverterRtdataDetailRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 16:08:50
 */
@DS("clickhouse")
@Service("inverterRtdataDetailRecordService")
public class InverterRtdataDetailRecordServiceImpl extends ServiceImpl<InverterRtdataDetailRecordMapper, InverterRtdataDetailRecord> implements InverterRtdataDetailRecordService {

    @Override
    public CommonPage<InverterRtdataDetailRecord> queryInverterDetailRecordPages(Integer currentPage, Integer size, Integer specialId) {
        LambdaQueryWrapper<InverterRtdataDetailRecord> queryWrapper = new LambdaQueryWrapper<>();
        Page<InverterRtdataDetailRecord> page = new Page<>(currentPage, size);
        queryWrapper.eq(InverterRtdataDetailRecord::getSpecial, specialId);
        // 运维中心提出特殊电站暂且处理
        ArrayList<String> list = new ArrayList<>();
        list.add("R5X2802J2446C06443");
        list.add("R5X2802J2446C06445");
        list.add("R5X2802J2446C06448");
        queryWrapper.notIn(InverterRtdataDetailRecord::getInverterId, list);
        this.page(page, queryWrapper);
        List<InverterRtdataDetailRecord> records = page.getRecords();
        String targetId = "R6I2253J2332C25221";
        boolean containsTargetId = records.stream()
                .anyMatch(record -> targetId.equals(record.getInverterId()));

        InverterRtdataDetailRecord sumRecord = null;
        if (containsTargetId) {
            records.removeIf(record -> targetId.equals(record.getInverterId()));
            sumRecord = baseMapper.getSumByInverterIds(list);
            records.add(sumRecord);
        }

        CommonPage<InverterRtdataDetailRecord> inverterRtdataDetailRecordCommonPage = CommonPage.pageInfo(page, page.getRecords());
        return inverterRtdataDetailRecordCommonPage;
    }

    @Override
    public CommonPage<InverterRtdataDetailRecord> queryInverterDetailRecordPages(InverterQueryDTO query) {
        LambdaQueryWrapper<InverterRtdataDetailRecord> queryWrapper = new LambdaQueryWrapper<>();
        Page<InverterRtdataDetailRecord> page = new Page<>(query.getPageNum(), query.getPageSize());
        queryWrapper.eq(InverterRtdataDetailRecord::getSpecial, query.getSpecial());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getInverterId()), InverterRtdataDetailRecord::getInverterId, query.getInverterId());
        this.page(page, queryWrapper);
        return CommonPage.pageInfo(page, page.getRecords());
    }
}

