package com.botong.serverinterface.pojo.dto;

import com.botong.enums.RequiredEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:43
 */
@Data
public class ModifyParamDTO {
    @ApiModelProperty("参数名")
    private String paramName;
    @ApiModelProperty("描述")
    @Size(max = 85, message = "描述过长，最大长度为85")
    private String paramDescription;
    @ApiModelProperty("参数值")
    private String paramValue;
    @ApiModelProperty("参数类型 eg:String、integer、float、double")
    private String paramType;
    @ApiModelProperty("是否必须 1：是,2:否")
    @JsonFormat
    private RequiredEnum required;
    @ApiModelProperty("请求类型 query/body")
    private String requestType;
}