package com.botong.entity.serverinterface.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
/**
 * (InterfaceClassification)表实体类
 *
 * <AUTHOR>
 * @since 2023-08-05 08:37:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode
@TableName("t_interface_classification")
public class InterfaceClassification  {
    @TableId
    private Long id;
    @ApiModelProperty("类别id")
    private Long classId;
    @ApiModelProperty("接口id")
    private Long interfaceId;
}

