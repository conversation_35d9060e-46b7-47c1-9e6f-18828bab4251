package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.system.User;
import com.botong.entity.system.UserRole;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/17.
 */

public interface UserRoleService extends IService<UserRole> {

    /**
     * 根据角色id删除角色用户管理关系
     *
     * @param roleIds 角色id数组
     */
    void deleteUserRolesByRoleId(List<String> roleIds);

    /**
     * 根据用户id删除角色用户管理关系
     *
     * @param userIds 用户id数组
     */
    void deleteUserRolesByUserId(List<Long> userIds);

    /**
     * 根据用户id获取用户与角色关联关系
     *
     * @param userId 用户id
     * @return 用户角色实体
     */
    UserRole getUserRoleByUserId(Long userId);


}
