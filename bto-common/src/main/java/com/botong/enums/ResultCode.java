package com.botong.enums;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public enum ResultCode {
    /**
     * 2xxx：成功：表示请求已被成功接收
     * 3xxx：重定向：要完成请求必须进行的一步的操作
     * 4xxx：客户端错误：请求有语法错误或请求无法实现
     * 5xxx：服务器端错误：服务器未能实现合法的请求
     */
    SUCCESS(2000, "请求成功"),
    ADD_SUCCESS(2001, "添加成功"),
    EDIT_SUCCESS(2002, "修改成功"),
    DELETE_SUCCESS(2003, "删除成功"),
    INVALID_TOKEN(4001, "token无效或已过期，请重新登录"),
    NO_GATEWAY_PATH(4002, "路径有误，请通过网关获取资源"),
    BAD_CREDENTIALS(4003, "认证失败"),
    DO_NOT_ALLOW_ACCESS(4005, "该URI不允许外部访问"),
    NOT_SUPPORTED_AUTHENTICATION_TYPE(4006, "不支持该认证类型"),
    INVALID_REFRESH_TOKEN(4007, "refresh token无效或已过期"),
    INVALID_ACCOUNT(4008, "账号无效，用户账号已被锁定，请联系管理员"),
    INVALID_USERNAME_OR_PASSWORD(4009, "用户名或密码错误，请重新登录"),
    INVALID_ORIGINAL_PASSWORD(4010, "原始密码有误"),
    FAIL(5000, "请求失败"),
    PARAM_ERROR(5001, "参数错误"),
    NO_PERMISSION(5002, "无权限"),
    USER_EXISTS(5003, "用户已存在"),
    FEIGN_ERROR(5004, "feign调用失败");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public String toString() {
        return super.toString();
    }
}