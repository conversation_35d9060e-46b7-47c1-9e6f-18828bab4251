spring:
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    lettuce:
      pool:
        min-idle: 8
        max-idle: 500
        max-active: 2000
        max-wait: 10000
    timeout: 5000
    password:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
  boot:
    admin:
      client:
        url: http://localhost:8401
        username: bto
        password: btoadmin
  datasource:
    dynamic:
      hikari:
        connection-timeout: 3000
        max-lifetime: 3000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: BtoHikariCP
      primary: base
      datasource:
        base:
          username: admin
          password: v25MViJ6SGLEEEax
#          username: root
#          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://************:3306/data_center?useSSL=false&serverTimezone=Hongkong&characterEncoding=utf-8&autoReconnect=true
#          url: ***********************************************************************************************************************