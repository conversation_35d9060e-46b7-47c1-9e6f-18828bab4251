package com.botong.serversystem.controller;

import cn.hutool.core.collection.CollUtil;
import com.botong.entity.vo.ResultVo;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.respones.ClassInterfaceTree;
import com.botong.serversystem.entity.respones.ClassificationVO;
import com.botong.serversystem.service.InterfaceClassificationService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * (InterfaceClassification)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-05 09:24:55
 */
@RestController
@Api(tags = "类别接口管理")
@RequestMapping("interfaceClass")
public class InterfaceClassificationController {
    @Autowired
    private InterfaceClassificationService interfaceClassificationService;

    @ApiOperation("类别接口树")
    @PostMapping("/classInterfaceTree")
    public ResultVo<List<ClassInterfaceTree>> classInterfaceTree(@RequestBody ClassificationVO classQueryDTO) {
        return interfaceClassificationService.classInterfaceTree(classQueryDTO);
    }

    @ApiOperation("类别更新接口(增删改)")
    @PostMapping("/interfaceClass")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classId", value = "类别id", paramType = "query", required = true),
            @ApiImplicitParam(name = "interfaceIds", value = "接口id", paramType = "body", required = true),
    })
    public ResultVo interfaceClass(@RequestParam Long classId, @RequestBody List<Long> interfaceIds) {
        if (classId == null) {
            throw new BtoException("类别id不能为空");
        }
        return interfaceClassificationService.interfaceClass(classId, interfaceIds);
    }

    @ApiOperation("根据classid获取接口id集合")
    @PostMapping("/listByClass")
    public ResultVo<List<Long>> listByClass(@ApiParam(value = "类别id", required = false)@RequestParam Long classId) {
        return interfaceClassificationService.listByClass(classId);
    }

    @ApiOperation("根据接口id删除 接口分类关联")
    @DeleteMapping("deleteByinterface")
    public ResultVo deleteInterfaceClassByinterface(@ApiParam(value = "部门id集合", required = true) @RequestBody List<Long> interfaceIds) {
        if (CollUtil.isEmpty(interfaceIds) ) {
            return ResultVo.fail("接口Id不能为空！");
        }
        return interfaceClassificationService.deleteByinterface(interfaceIds);
    }

}

