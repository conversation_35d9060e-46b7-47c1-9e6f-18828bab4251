package com.botong.entity.dto;

import com.botong.enums.SortTpyEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/7/17 10:28
 */
@ToString
public class SortType {
    @ApiModelProperty("排序字段（1：createTime 2：modifyTime）")
    private String sortField;
    @ApiModelProperty("是否升序1:true 2:false")
    private boolean isAsc;

    public SortType(String sortField, String isAsc) {
        setSortField(sortField);
        setIsAsc(isAsc);
    }

    public String getSortField() {
        return sortField;
    }

    public boolean getIsAsc() {
        return isAsc;
    }

    public void setSortField(String sortField) {
        switch (sortField) {
            case "2":
                this.sortField =  SortTpyEnum.SORT_MODIFY_TIME.getName();
                break;
            default:
                this.sortField =  SortTpyEnum.SORT_CREATE_TIME.getName();
                break;
        }
    }

    public void setIsAsc(String isAsc) {
        switch (isAsc) {
            case "1":
                this.isAsc = true;
                break;
            default:
                this.isAsc = false;
                break;
        }
    }



    public static void main(String[] args) {
        SortType sortType = new SortType("1", "2");
        // SortType sortType = new SortType();
        // System.out.println(sortType);
    }
}

