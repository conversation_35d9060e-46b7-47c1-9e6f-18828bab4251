package com.botong.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.botong.constant.BtoConstant;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Base64Utils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class BtoServerProtectInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        // 从请求头中获取 Gateway Token
        String token = request.getHeader(BtoConstant.GATEWAY_TOKEN_HEADER);
        String gatewayToken = new String(Base64Utils.encode(BtoConstant.GATEWAY_TOKEN_VALUE.getBytes()));
        // 校验 Gateway Token的正确性
        if (StringUtils.equals(gatewayToken, token)) {
            return true;
        } else {
            response.setContentType(BtoConstant.APPLICATION_JSON_UTF8_VALUE);
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            ResultVo<Object> fail = ResultVo.fail(ResultCode.NO_GATEWAY_PATH);
            response.getWriter().write(JSONObject.toJSONString(fail));
            return false;
        }
    }
}