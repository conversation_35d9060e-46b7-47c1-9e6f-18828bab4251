package com.botong.serversystem.controller;

import com.botong.utils.JwtUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
public class TestController {

    @GetMapping("info")
    public String test() {
        return "server-system";
    }

    @GetMapping("user")
    public Principal currentUser(Principal principal) {
        return principal;
    }

    @GetMapping("system")
    public String system(String name) {
        return "system" + name;
    }

    @GetMapping("getUserId")
    public Object system() {
        return JwtUtil.getCurrentUserId();
    }

}