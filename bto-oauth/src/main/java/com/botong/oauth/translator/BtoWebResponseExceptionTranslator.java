package com.botong.oauth.translator;

import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.common.exceptions.UnsupportedGrantTypeException;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
@Component
public class BtoWebResponseExceptionTranslator implements WebResponseExceptionTranslator<OAuth2Exception> {

    private static final String INVALID_REFRESH_TOKEN = "invalid refresh token";
    private static final String LOCKED = "locked";

    @Override
    public ResponseEntity translate(Exception e) {
        ResponseEntity.BodyBuilder status = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
        String message = ResultCode.BAD_CREDENTIALS.getMessage();
        log.error(message, e);
        if (e instanceof UnsupportedGrantTypeException) {
            return status.body(ResultVo.fail(ResultCode.NOT_SUPPORTED_AUTHENTICATION_TYPE));
        }
        if (e instanceof InvalidGrantException) {
            if (StringUtils.containsIgnoreCase(e.getMessage(), INVALID_REFRESH_TOKEN)) {
                return status.body(ResultVo.fail(ResultCode.INVALID_REFRESH_TOKEN));
            }
            if (StringUtils.containsIgnoreCase(e.getMessage(), LOCKED)) {
                return status.body(ResultVo.fail(ResultCode.INVALID_ACCOUNT));
            }
            return status.body(ResultVo.fail(ResultCode.INVALID_USERNAME_OR_PASSWORD));
        }
        return status.body(ResultVo.fail(ResultCode.BAD_CREDENTIALS));
    }
}