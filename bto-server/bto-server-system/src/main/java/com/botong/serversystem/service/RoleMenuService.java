package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.system.RoleMenu;
import com.botong.serversystem.entity.requerst.RoleMenuDTO;

import java.util.List;


/**
 * 角色菜单关联表
 *
 * <AUTHOR> by zhb on 2023/7/17.
 */
public interface RoleMenuService extends IService<RoleMenu> {

    /**
     * 删除角色菜单关联数据
     *
     * @param roleIds 角色id
     */
    void deleteRoleMenusByRoleId(List<Long> roleIds);

    /**
     * 删除角色菜单关联数据
     *
     * @param menuIds 菜单id
     */
    void deleteRoleMenusByMenuId(List<Long> menuIds);

    /**
     * 获取角色对应的菜单列表
     *
     * @param roleId 角色id
     * @return 菜单列表
     */
    List<RoleMenu> getRoleMenusByRoleId(Long roleId);

    /**
     * 根据角色id授权角色菜单
     *
     * @param roleMenuDTO 角色id集合
     */
    void authorizationMenu(RoleMenuDTO roleMenuDTO);
}

