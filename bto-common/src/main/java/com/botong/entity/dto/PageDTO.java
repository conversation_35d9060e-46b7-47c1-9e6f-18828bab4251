package com.botong.entity.dto;

import com.github.pagehelper.IPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 通用分页查询DTO
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Getter
@Setter
@ToString
public class PageDTO implements IPage {
    @ApiModelProperty(value = "当前页数")
    private Integer pageNum;

    @ApiModelProperty(value = "每页个数")
    private Integer pageSize;

    @ApiModelProperty(value = "排序", hidden = true)
    private String orderBy;

    {
        pageNum = 1;
        pageSize = 10;
    }

    @Override
    public Integer getPageNum() {
        return pageNum;
    }

    @Override
    public Integer getPageSize() {
        return pageSize;
    }

    @Override
    public String getOrderBy() {
        return orderBy;
    }
}
