package com.botong.entity.serverinterface.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * (DeptInterface)表实体类
 *
 * <AUTHOR>
 * @since 2023-08-03 14:42:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("t_dept_interface")
public class DeptInterface {
    @TableId
    private Long id;
    @ApiModelProperty("部门id")
    private Long deptId;
    @ApiModelProperty("接口id")
    private Long interfaceId;
}

