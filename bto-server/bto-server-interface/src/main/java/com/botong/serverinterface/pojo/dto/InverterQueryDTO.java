package com.botong.serverinterface.pojo.dto;

import com.botong.entity.dto.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> by zhb on 2024/4/29.
 */

@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class InverterQueryDTO extends PageDTO {

    @ApiModelProperty(value = "逆变器ID")
    private String inverterId;


    @ApiModelProperty(value = "项目ID", hidden = true)
    private Integer special;
}