package com.botong.serverinterface.controller;

import com.botong.serverinterface.pojo.dto.InverterAlarmDTO;
import com.botong.serverinterface.service.PhotovoltaicService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> by zhb on 2023/8/8.
 */
@Api(tags = "第三方系统光伏运维系统接口")
@RestController
@RequestMapping("pv")
public class PhotovoltaicController {

    @Autowired
    private PhotovoltaicService photovoltaicService;

    @PostMapping("getInverterAlarm")
    public Object getInverterAlarm(@RequestBody InverterAlarmDTO inverterAlarmDTO) {
        return this.photovoltaicService.getInverterAlarm(inverterAlarmDTO);
    }


    @GetMapping("getInverterAlarmDict")
    public Object getInverterAlarmDict() {
        return this.photovoltaicService.getInverterAlarmDict();
    }


}
