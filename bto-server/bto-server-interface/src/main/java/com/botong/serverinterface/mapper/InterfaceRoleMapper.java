package com.botong.serverinterface.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.entity.serverinterface.entity.InterfaceRole;
import com.botong.serverinterface.pojo.vo.InterfaceTree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 接口角色关联表(InterfaceRole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-18 09:04:11
 */
@Mapper
public interface InterfaceRoleMapper extends BaseMapper<InterfaceRole> {
   /**
    * 获取接口树
    * @param roleIds
    * @return
    */
   List<InterfaceTree> getInterfaceTree(@Param("roleIds") List<Long> roleIds);

}

