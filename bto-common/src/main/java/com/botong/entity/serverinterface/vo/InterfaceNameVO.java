package com.botong.entity.serverinterface.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/26 17:57
 */

@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class InterfaceNameVO {
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("接口id")
    private Long classId;
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InterfaceNameVO that = (InterfaceNameVO) o;
        return Objects.equals(interfaceId, that.interfaceId) &&
                Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(interfaceId, name);
    }
}
