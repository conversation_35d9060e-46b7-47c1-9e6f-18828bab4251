package com.botong.entity.serverinterface.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @since 2024/11/21 9:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InverterDayElectricityVO {
    @ApiModelProperty("电站名")
    private String plantName;
    @ApiModelProperty("电站uid")
    private String plantUid;
    @ApiModelProperty("逆变器id")
    private String inverterId;
    @ApiModelProperty("今日发电量（单位：kwp）")
    private String todayElectricity;
    @ApiModelProperty("数据采集时间")
    @JsonFormat(pattern = "yy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp collectTime;
}
