package com.botong.serversystem.entity.respones;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class ClassInterfaceVO {
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("类别id")
    private Long classId;
}