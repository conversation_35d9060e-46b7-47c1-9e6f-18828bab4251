package com.botong.serverinterface.fallback;

import com.botong.enums.ResultCode;
import com.botong.serverinterface.feign.PhotovoltaicFeign;
import com.botong.serverinterface.pojo.dto.InverterAlarmDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */

@Component
@Slf4j
public class PhotovoltaicFeignFactory implements PhotovoltaicFeign {

    @Override
    public Object getInverterAlarm(InverterAlarmDTO inverterAlarmDTO) {
        return ResultCode.FEIGN_ERROR.getMessage();
    }

    @Override
    public Object getInverterAlarmDict(ArrayList<String> list) {
        return  ResultCode.FEIGN_ERROR.getMessage();
    }

}