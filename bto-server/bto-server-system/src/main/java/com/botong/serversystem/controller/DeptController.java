package com.botong.serversystem.controller;

import cn.hutool.core.util.ObjectUtil;
import com.botong.entity.system.Dept;
import com.botong.entity.system.Tree;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.respones.DeptInfoVO;
import com.botong.serversystem.service.DeptService;
import com.botong.utils.NullUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
@Validated
@Api(tags = "部门管理")
@RestController
@RequestMapping("dept")
@RequiredArgsConstructor
public class DeptController {

    private final DeptService deptService;

    /**
     * 获取部门列表
     *
     * @param deptName 查询参数
     * @return 部门集合
     */
    @ApiOperation("获取部门列表")
    @GetMapping("/list")
    public ResultVo<List<? extends Tree<?>>> getList(String deptName) {
        List<? extends Tree<?>> deptList = deptService.selectList(deptName);
        return ResultVo.success(deptList);
    }

    /**
     * 根据主键获取部门详情
     *
     * @param deptId 主键
     * @return 部门实体
     */
    @ApiOperation("根据主键获取部门详情")
    @GetMapping("/info/{deptId}")
    public ResultVo<Dept> info(@PathVariable("deptId") Long deptId) {
        boolean result = NullUtils.isNull(deptId);
        if (result) {
            String message = "deptId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            Dept dept = deptService.info(deptId);
            return ResultVo.success(dept);
        }
    }

    /**
     * 新增部门
     *
     * @param dept 实体
     */
    @ApiOperation("新增部门")
    @PostMapping("/add")
    public ResultVo<Void> add(@RequestBody Dept dept) {
        deptService.createDept(dept);
        return ResultVo.success(ResultCode.ADD_SUCCESS);
    }

    /**
     * 根据主键修改信息
     *
     * @param dept 实体
     */
    @ApiOperation("根据主键修改信息")
    @PostMapping("/update")
    public ResultVo<Void> update(@RequestBody Dept dept) {
        boolean result = NullUtils.isNull(dept.getDeptId());
        if (result) {
            String message = "deptId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            deptService.updateDept(dept);
            return ResultVo.success(ResultCode.EDIT_SUCCESS);
        }
    }

    /**
     * 根据主键删除部门信息
     *
     * @param deptIds 部门id集合
     */
    @ApiOperation("批量删除")
    @PostMapping("/delete")
    public ResultVo<Void> delete(@RequestBody List<Long> deptIds) {
        boolean result = NullUtils.isNull(deptIds);
        if (result) {
            String message = "deptIds must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        }
        deptService.deleteById(deptIds);
        return ResultVo.success(ResultCode.DELETE_SUCCESS);
    }

    @ApiOperation("根据接口id获取部门信息")
    @GetMapping("/info")
    public ResultVo<List<DeptInfoVO>> getDeptByInterfaceId(@RequestParam Long interfaceId) {
        if (ObjectUtil.isNull(interfaceId)) {
            throw new BtoException("id不能为空");
        }
        return ResultVo.success(deptService.getDeptByInterfaceId(interfaceId));
    }

}
