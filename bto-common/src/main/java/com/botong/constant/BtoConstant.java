package com.botong.constant;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public interface BtoConstant {

    /**
     * GATEWAY请求头TOKEN名称（不要有空格）
     */
    String GATEWAY_TOKEN_HEADER = "GatewayToken";
    /**
     * GATEWAY请求头TOKEN值
     */
    String GATEWAY_TOKEN_VALUE = "bto:gateway:123456";

    /**
     * gif类型
     */
    String GIF = "gif";
    /**
     * png类型
     */
    String PNG = "png";

    /**
     * 验证码 key前缀
     */
    String CODE_PREFIX = "bto.captcha.";
    /**
     * 验证码数字类型
     */
    String CAPTCHA_TYPE_NUMBER = "number";
    /**
     * 验证码图片类型
     */
    String CAPTCHA_TYPE_PICTURE = "picture";

    /**
     * 响应内容类型
     */
    String APPLICATION_JSON_UTF8_VALUE = "application/json;charset=UTF-8";

    /**
     * jwt SigningKey
     */
    String SIGNING_KEY = "bto:api";

    /**
     * 网关限流分组名称
     */
    String SENTINEL_GROUP_NAME_SYSTEM = "sentinelGroupSystem";

    /**
     * 未分类id
     */
    Long  UNDEFINE_CLASS_ID = -1L;
    String SENTINEL_GROUP_NAME_INTERFACE = "sentinelGroupInterface";
}