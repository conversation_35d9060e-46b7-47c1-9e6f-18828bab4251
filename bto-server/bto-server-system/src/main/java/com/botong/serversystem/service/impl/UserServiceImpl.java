package com.botong.serversystem.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.system.Dept;
import com.botong.entity.system.Role;
import com.botong.entity.system.User;
import com.botong.entity.system.UserRole;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.requerst.UserQueryDTO;
import com.botong.serversystem.entity.respones.SystemUserVo;
import com.botong.serversystem.mapper.UserMapper;
import com.botong.serversystem.service.DeptService;
import com.botong.serversystem.service.RoleService;
import com.botong.serversystem.service.UserRoleService;
import com.botong.serversystem.service.UserService;
import com.botong.utils.JwtUtil;
import com.botong.utils.NullUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final PasswordEncoder passwordEncoder;
    private final DeptService deptService;
    private final UserRoleService userRoleService;
    private final RoleService roleService;


    @Override
    public Boolean findUserIsExists(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(User::getUsername, username);
        return this.baseMapper.exists(queryWrapper);
    }

    @Override
    public PageInfo<SystemUserVo> getList(UserQueryDTO query) {

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StringUtils.isNotBlank(query.getUsername()), User::getUsername, query.getUsername());
        queryWrapper.lambda().like(StringUtils.isNotBlank(query.getEmail()), User::getEmail, query.getEmail());
        queryWrapper.lambda().like(StringUtils.isNotBlank(query.getStatus()), User::getStatus, query.getStatus());
        queryWrapper.lambda().like(StringUtils.isNotBlank(query.getMobile()), User::getMobile, query.getMobile());
        Dept dept = deptService.getDeptByName(query.getDeptName());
        if (NullUtils.isNotNull(dept)) {
            queryWrapper.lambda().like(StringUtils.isNotBlank(dept.getDeptId().toString()), User::getDeptId, dept.getDeptId());
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<User> users = this.baseMapper.selectList(queryWrapper);
        ArrayList<SystemUserVo> systemUserVos = new ArrayList<>();
        for (User user : users) {
            SystemUserVo systemUserVo = new SystemUserVo();
            BeanUtils.copyProperties(user, systemUserVo);
            String deptName = "";
            Dept entity = deptService.getById(user.getDeptId());
            if (NullUtils.isNotNull(entity)) {
                deptName = entity.getDeptName();
            }
            systemUserVo.setDeptName(deptName);
            UserRole userRole = userRoleService.getUserRoleByUserId(user.getUserId());
            if (NullUtils.isNotNull(userRole)) {
                Role role = roleService.getById(userRole.getRoleId());
                if (NullUtils.isNotNull(role)) {
                    systemUserVo.setRoleId(role.getRoleId());
                    systemUserVo.setRoleName(role.getRoleName());
                    systemUserVo.setPassword(null);
                    systemUserVos.add(systemUserVo);
                }
            }
        }
        PageInfo<User> source = new PageInfo<>(users);
        PageInfo<SystemUserVo> target = new PageInfo<>();
        BeanUtils.copyProperties(source, target);
        target.setList(systemUserVos);
        return target;
    }

    @Override
    public SystemUserVo info(Long userId) {
        SystemUserVo systemUserVo = new SystemUserVo();
        User user = getById(userId);
        BeanUtils.copyProperties(user, systemUserVo);
        String deptName = deptService.getById(user.getDeptId()).getDeptName();
        systemUserVo.setDeptName(deptName);
        UserRole userRole = userRoleService.getUserRoleByUserId(user.getUserId());
        Role role = roleService.getById(userRole.getRoleId());
        systemUserVo.setRoleId(role.getRoleId());
        systemUserVo.setRoleName(role.getRoleName());
        systemUserVo.setPassword(null);
        return systemUserVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<Object> add(SystemUserVo user) {
        // 判断用户是否存在
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(User::getUsername, user.getUsername());
        boolean exists = this.getBaseMapper().exists(queryWrapper);
        if (Boolean.TRUE.equals(exists)) {
            return ResultVo.fail(ResultCode.USER_EXISTS);
        }
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        boolean saveUserResult = save(user);
        // 保存用户角色关系
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getUserId());
        userRole.setRoleId(user.getRoleId());
        boolean saveUserRoleResult = userRoleService.save(userRole);
        if (Boolean.FALSE.equals(saveUserResult) && Boolean.FALSE.equals(saveUserRoleResult)) {
            throw new BtoException(ResultCode.FAIL);
        } else {
            return ResultVo.success(ResultCode.ADD_SUCCESS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<Object> update(SystemUserVo user) {
        user.setPassword(null);
        boolean updateUserResult = updateById(user);
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getUserId());
        userRole.setRoleId(user.getRoleId());
        // 修改角色
        UpdateWrapper<UserRole> queryWrapper = new UpdateWrapper<>();
        queryWrapper.lambda().eq(UserRole::getUserId, user.getUserId()).set(UserRole::getRoleId, user.getRoleId());
        boolean updateUserRoleResult = userRoleService.update(queryWrapper);

        if (Boolean.FALSE.equals(updateUserResult) && Boolean.FALSE.equals(updateUserRoleResult)) {
            throw new BtoException(ResultCode.FAIL);
        } else {
            return ResultVo.success(ResultCode.EDIT_SUCCESS);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<Object> delete(List<Long> userIds) {
        boolean removeUserResult = removeByIds(userIds);
        userRoleService.deleteUserRolesByUserId(userIds);
        if (Boolean.FALSE.equals(removeUserResult)) {
            throw new BtoException(ResultCode.FAIL);
        } else {
            return ResultVo.success(ResultCode.DELETE_SUCCESS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(String password) {
        User user = new User();
        user.setPassword(passwordEncoder.encode(password));
        Long userId = Convert.toLong(JwtUtil.getCurrentUserId());
        this.baseMapper.update(user, new LambdaQueryWrapper<User>().eq(User::getUserId, userId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(List<Long> userIds) {
        User user = new User();
        user.setPassword(passwordEncoder.encode(User.DEFAULT_PASSWORD));
        this.baseMapper.update(user, new LambdaUpdateWrapper<User>().in(User::getUserId, userIds));
    }

}
