package com.botong.exception;

import com.botong.enums.ResultCode;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class BtoException extends RuntimeException {
    private final Integer errorCode;
    private final String errorMessage;

    public BtoException() {
        super(ResultCode.FAIL.getMessage());
        this.errorCode = ResultCode.FAIL.getCode();
        this.errorMessage = ResultCode.FAIL.getMessage();
    }

    public BtoException(ResultCode exceptionCode) {
        super(exceptionCode.getMessage());
        this.errorCode = exceptionCode.getCode();
        this.errorMessage = exceptionCode.getMessage();
    }

    public BtoException(Integer errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public BtoException(String errorMessage) {
        super(errorMessage);
        this.errorCode = ResultCode.FAIL.getCode();
        this.errorMessage = errorMessage;
    }

    public Throwable fillInStackTrace() {
        return this;
    }
}
