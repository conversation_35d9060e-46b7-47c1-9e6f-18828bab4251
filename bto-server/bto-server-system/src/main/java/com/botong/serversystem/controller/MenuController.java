package com.botong.serversystem.controller;

import java.util.List;

import com.botong.entity.router.VueRouter;
import com.botong.entity.system.Menu;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.serversystem.service.MenuService;
import com.botong.utils.NullUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@RestController
@RequestMapping("menu")
@Api(tags = "菜单管理")
@Slf4j
@Validated
@RequiredArgsConstructor
public class MenuController {

    private final MenuService menuService;

    /**
     * 获取菜单列表
     *
     * @param title 查询参数
     * @return 菜单集合
     */
    @ApiOperation("获取菜单列表")
    @GetMapping("/list")
    public ResultVo<List<VueRouter<Menu>>> getList(String title) {
        List<VueRouter<Menu>> menus = menuService.getList(title);
        return ResultVo.success(menus);
    }

    /**
     * 根据主键获取菜单详情
     *
     * @param menuId 主键
     * @return 菜单实体
     */
    @ApiOperation("根据主键获取菜单详情")
    @GetMapping("/info/{menuId}")
    public ResultVo<Menu> info(@PathVariable("menuId") Long menuId) {
        boolean result = NullUtils.isNull(menuId);
        if (result) {
            String message = "menuId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            Menu menu = menuService.info(menuId);
            return ResultVo.success(menu);
        }
    }

    /**
     * 新增菜单
     *
     * @param menu 实体
     */
    @ApiOperation("新增菜单")
    @PostMapping("/add")
    public ResultVo<Void> add(@RequestBody Menu menu) {
        menuService.add(menu);
        return ResultVo.success(ResultCode.ADD_SUCCESS);
    }


    /**
     * 根据主键修改信息
     *
     * @param menu 实体
     */
    @ApiOperation("根据主键修改信息")
    @PostMapping("/update")
    public ResultVo<Void> update(@RequestBody Menu menu) {
        boolean result = NullUtils.isNull(menu.getMenuId());
        if (result) {
            String message = "menuId must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        } else {
            menuService.update(menu);
            return ResultVo.success(ResultCode.EDIT_SUCCESS);
        }
    }


    /**
     * 根据主键删除菜单信息
     *
     * @param menuIds 菜单id集合
     */
    @ApiOperation("批量删除")
    @PostMapping("/delete")
    public ResultVo<Void> delete(@RequestBody List<Long> menuIds) {
        boolean result = NullUtils.isNull(menuIds);
        if (result) {
            String message = "menuIds must not be empty";
            log.error(message);
            return ResultVo.fail(message);
        }
        menuService.delete(menuIds);
        return ResultVo.success(ResultCode.DELETE_SUCCESS);
    }


}
