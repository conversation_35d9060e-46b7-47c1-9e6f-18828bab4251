<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.oauth.mapper.UserMapper">
    <select id="findByName" parameterType="string" resultType="com.botong.oauth.entity.UserDetails">
        SELECT
            u.user_id userId,
            u.username,
            u.email,
            u.mobile,
            u.password,
            u.status,
            u.create_time createTime,
            u.gender gender,
            u.dept_id deptId,
            u.last_login_time lastLoginTime,
            u.modify_time modifyTime,
            u.description,
            u.avatar,
            d.dept_name deptName,
            GROUP_CONCAT(r.role_id) roleId,
            GROUP_CONCAT(r.ROLE_NAME) roleName
        FROM
            t_user u
                LEFT JOIN t_dept d ON (u.dept_id = d.dept_id)
                LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id)
                LEFT JOIN t_role r ON r.role_id = ur.role_id
        WHERE  u.username = #{username}
        group by u.username,u.user_id,u.email,u.mobile,u.password, u.status,u.create_time,u.gender,u.dept_id
               ,u.last_login_time,u.modify_time,u.description,u.avatar
    </select>
</mapper>