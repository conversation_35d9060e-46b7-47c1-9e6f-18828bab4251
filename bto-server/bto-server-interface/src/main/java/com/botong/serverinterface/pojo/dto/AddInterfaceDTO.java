package com.botong.serverinterface.pojo.dto;

import com.botong.enums.RequestMethodEnum;
import com.botong.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/14 11:46
 */
@Data
public class AddInterfaceDTO {
    @NotEmpty(message = "接口名称不能为空")
    @ApiModelProperty("接口名称")
    private String name;
    @Size(max = 85, message = "描述过长，最大长度为85")
    @ApiModelProperty("接口描述")
    private String interfaceDescribe;

    @NotNull(message = "接口请求方式不能为空")
    @ApiModelProperty("接口请求方式，1:post,2:get,3:put,4:delete")
    @JsonFormat
    private RequestMethodEnum requestMethod;

    @NotEmpty(message = "接口url不能为空")
    @ApiModelProperty("接口url")
    private String url;

    @ApiModelProperty("请求头")
    private String requestHeader;

    @ApiModelProperty("响应头")
    private String responseHeader;
    @NotNull(message = "状态不能为空")
    @ApiModelProperty("接口状态 0:失效，1:正常")
    @JsonFormat
    private StatusEnum status;
    @ApiModelProperty("请求参数")
    private List<ModifyParamDTO> requestParas;
}
