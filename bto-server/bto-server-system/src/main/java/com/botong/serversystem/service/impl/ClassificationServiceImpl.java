package com.botong.serversystem.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.api.module.serverinterface.InterfaceApi;
import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.Classification;
import com.botong.entity.serverinterface.entity.InterfaceClassification;
import com.botong.entity.vo.ResultVo;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.requerst.AddClassDTO;
import com.botong.serversystem.entity.requerst.ClassQueryDTO;
import com.botong.serversystem.entity.respones.ClassificationVO;
import com.botong.serversystem.mapper.ClassificationMapper;
import com.botong.serversystem.service.ClassificationService;
import com.botong.serversystem.service.DeptInterfaceService;
import com.botong.serversystem.service.InterfaceClassificationService;
import com.botong.utils.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/26 9:52
 */
@Transactional(rollbackFor = Exception.class)
@Service("ClassificationService")
public class ClassificationServiceImpl extends ServiceImpl<ClassificationMapper, Classification> implements ClassificationService {


    @Autowired
    private DeptInterfaceService deptInterfaceService;
    @Autowired
    private InterfaceClassificationService interfaceClassificationService;
    @Autowired
    private InterfaceApi interfaceApi;

    @Override
    public ResultVo addClass(AddClassDTO addClassDTO) {
        LambdaQueryWrapper<Classification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Classification::getClassName, addClassDTO.getClassName());
        List<Classification> classifications = baseMapper.selectList(queryWrapper);
        if (classifications.size() > 0) {
            throw new BtoException("类别名已存在");
        }
        Classification classification = BeanCopyUtils.copyBean(addClassDTO, Classification.class);

        if (this.save(classification)) {
            return ResultVo.success("操作成功");
        }
        throw new BtoException("操作失败");
    }

    @Override
    public ResultVo deleteClassById(List<Long> classIds) {
        LambdaQueryWrapper<InterfaceClassification> ifQueryWrapper = new LambdaQueryWrapper<>();
        ifQueryWrapper.in(CollUtil.isNotEmpty(classIds), InterfaceClassification::getClassId, classIds);
        interfaceClassificationService.remove(ifQueryWrapper);
        if (this.removeBatchByIds(classIds)) {
            return ResultVo.success("操作成功");
        }
        throw new BtoException("操作失败");
    }

    @Override
    public ResultVo updateClass(ClassificationVO updateClassDTO) {
        List<Classification> classifications = this.lambdaQuery()
                .eq(Classification::getClassName, updateClassDTO.getClassName())
                .ne(Classification::getClassId, updateClassDTO.getClassId())
                .list();
        if (CollUtil.isNotEmpty(classifications)) {
            throw new BtoException("类别名已存在");
        }
        Classification classification = BeanCopyUtils.copyBean(updateClassDTO, Classification.class);
        if (this.updateById(classification)) {
            return ResultVo.success("操作成功");
        }
        throw new BtoException("操作失败");
    }



    @Override
    public List<ClassificationVO> list(ClassQueryDTO classQueryDTO, Long deptId) {
        List<Long> interfaceIds = deptInterfaceService.list(deptId);
        if (CollUtil.isEmpty(interfaceIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InterfaceClassification> icQueryWrapper = new LambdaQueryWrapper<>();
        icQueryWrapper.in(InterfaceClassification::getInterfaceId, interfaceIds);
        List<Long> classIds = interfaceClassificationService.list(icQueryWrapper).stream()
                .map(InterfaceClassification::getClassId)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(classIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Classification> classQueryWrapper = new LambdaQueryWrapper<>();
        classQueryWrapper.in(CollUtil.isNotEmpty(classIds), Classification::getClassId, classIds);
        List<Classification> classList = baseMapper.selectList(classQueryWrapper);

        return BeanCopyUtils.copyBeanList(classList, ClassificationVO.class);
    }

    @Override
    public CommonPage<ClassificationVO> selectAll(ClassQueryDTO classQueryDTO) {
        LambdaQueryWrapper<Classification> classQueryWrapper = new LambdaQueryWrapper<>();
        if (classQueryDTO != null) {
            classQueryWrapper.like(StrUtil.isNotEmpty(classQueryDTO.getClassName()), Classification::getClassName, Optional.ofNullable(classQueryDTO.getClassName()).orElse(""));
        }
        Page<Classification> page = new Page<>(classQueryDTO.getPageNum(), classQueryDTO.getPageSize());
        this.page(page, classQueryWrapper);
        List<ClassificationVO> interfaceClassificationList = BeanCopyUtils.copyBeanList(page.getRecords(), ClassificationVO.class);
        return CommonPage.pageInfo(page, interfaceClassificationList);

    }


    @Override
    public List<ClassificationVO> listByInterfaceId(Long interfaceId) {
        if (interfaceId == null) {
            throw new BtoException("接口id不能为空");
        }
        // 查询InterfaceClassification列表
        List<InterfaceClassification> list = interfaceClassificationService.lambdaQuery()
                .eq(InterfaceClassification::getInterfaceId, interfaceId)
                .list();
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取所有classId
        List<Long> classIds = list.stream()
                .map(InterfaceClassification::getClassId)
                .distinct()
                .collect(Collectors.toList());

        if (classIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询Classification列表
        List<Classification> classList = this.lambdaQuery()
                .in(Classification::getClassId, classIds)
                .list();
        if (classList == null || classList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyBeanList(classList, ClassificationVO.class);
    }

    @Override
    public ClassificationVO selectOne(Long classId) {
        if(classId == null){
            throw new BtoException("类别id不能为空");
        }
        Classification classification = baseMapper.selectOne(new LambdaQueryWrapper<Classification>().eq(Classification::getClassId, classId));
        if (ObjectUtil.isNull(classification)) {
            throw new BtoException("类别不存在");
        }
        return  BeanCopyUtils.copyBean(classification, ClassificationVO.class);
    }

}
