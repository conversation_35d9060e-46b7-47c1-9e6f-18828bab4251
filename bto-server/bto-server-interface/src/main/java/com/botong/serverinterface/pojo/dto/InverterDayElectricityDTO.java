package com.botong.serverinterface.pojo.dto;

import com.botong.entity.dto.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class InverterDayElectricityDTO extends PageDTO {
    @ApiModelProperty(value = "需要查询的电站uid")
    private String plantUid;
    @ApiModelProperty("需要查询的逆变器id")
    private String inverterId;
    @ApiModelProperty("需要查询的电站名")
    private String plantName;
    @ApiModelProperty(value = "项目ID",hidden = true)
    private Integer special;
    @ApiModelProperty(value = "查询条件：开始时间")
    private String startTime;
    @ApiModelProperty(value = "查询条件：结束时间")
    private String endTime;
}
