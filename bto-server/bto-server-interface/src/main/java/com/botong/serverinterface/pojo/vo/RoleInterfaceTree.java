package com.botong.serverinterface.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/13 11:47
 */
@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class RoleInterfaceTree {
    @ApiModelProperty(value = "角色 ID")
    private Long roleId;
    @TableField(value = "ROLE_NAME")
    @ApiModelProperty(value = "角色名称", required = true)
    private String roleName;
    private List<InterfaceTree> interfaceTreeList;
}
