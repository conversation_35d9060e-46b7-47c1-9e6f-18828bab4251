package com.botong.gateway.handler;

import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.ResourceProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.DefaultErrorWebExceptionHandler;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.cloud.gateway.support.TimeoutException;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.reactive.function.server.*;
import org.springframework.web.server.ResponseStatusException;

import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
public class BtoGatewayExceptionHandler extends DefaultErrorWebExceptionHandler {

    private static final String CONNECTION_REFUSED = "connection refused";

    public BtoGatewayExceptionHandler(ErrorAttributes errorAttributes, ResourceProperties resourceProperties,
                                      ErrorProperties errorProperties, ApplicationContext applicationContext) {
        super(errorAttributes, resourceProperties, errorProperties, applicationContext);
    }

    /**
     * 异常处理，定义返回报文格式
     */
    @Override
    protected Map<String, Object> getErrorAttributes(ServerRequest request, boolean includeStackTrace) {
        Throwable error = super.getError(request);
        ServerHttpRequest serverRequest = request.exchange().getRequest();
        InetSocketAddress remoteAddress = serverRequest.getRemoteAddress();

        // 尝试从X-Forwarded-For头获取真实IP
        String realIP = serverRequest.getHeaders().getFirst("X-Forwarded-For");
        if (realIP == null) {
            // 如果X-Forwarded-For头不存在，使用remoteAddress
            realIP = remoteAddress.getHostString();
        } else {
            // 如果使用了X-Forwarded-For，可能需要处理多个IP地址的情况
            // 通常，第一个IP是原始客户端的IP，但这也取决于代理的配置
            String[] forwardedIPs = realIP.split(",");
            if (forwardedIPs.length > 0) {
                realIP = forwardedIPs[0].trim();
            }
        }
        log.error(
                "请求发生异常，请求URI：{}，请求方法：{}，异常信息：{}，请求IP地址：{}",
                request.path(), request.methodName(), error.getMessage(), realIP
        );
        String errorMessage;
        if (error instanceof NotFoundException) {
            String serverId = StringUtils.substringAfterLast(error.getMessage(), "Unable to find instance for ");
            serverId = StringUtils.replace(serverId, "\"", StringUtils.EMPTY);
            errorMessage = String.format("无法找到%s服务", serverId);
        } else if (StringUtils.containsIgnoreCase(error.getMessage(), CONNECTION_REFUSED)) {
            errorMessage = "目标服务拒绝连接";
        } else if (error instanceof TimeoutException) {
            errorMessage = "访问服务超时";
        } else if (error instanceof ResponseStatusException
                && StringUtils.containsIgnoreCase(error.getMessage(), HttpStatus.NOT_FOUND.toString())) {
            errorMessage = "未找到该资源";
        } else if (error instanceof ParamFlowException) {
            errorMessage = "访问频率超限，请稍候重试";
        } else {
            log.error("网关转发异常信息：" + error.getMessage());
            errorMessage = "服务繁忙，请稍后再试";
        }
        Map<String, Object> errorAttributes = new HashMap<>(3);
        errorAttributes.put("message", errorMessage);
        return errorAttributes;
    }

    @Override
    @SuppressWarnings("all")
    protected RouterFunction<ServerResponse> getRoutingFunction(ErrorAttributes errorAttributes) {
        return RouterFunctions.route(RequestPredicates.all(), this::renderErrorResponse);
    }

    @Override
    protected int getHttpStatus(Map<String, Object> errorAttributes) {
        return HttpStatus.INTERNAL_SERVER_ERROR.value();
    }
}