package com.botong.oauth.service;

import com.botong.constant.BtoConstant;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.exception.ValidateCodeException;
import com.botong.oauth.properties.BtoAuthProperties;
import com.botong.oauth.properties.BtoValidateCodeProperties;
import com.botong.service.RedisService;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Service
public class ValidateCodeService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private BtoAuthProperties properties;

    /**
     * 生成验证码
     *
     * @param key          验证码Key值
     * @param response     HttpServletResponse
     * @param responseType 返回验证码格式，图片和数字，图片方式可以直接在src中进行请求
     */
    public ResultVo<Object> createVerificationCode(String key, HttpServletResponse response, String responseType) throws IOException, ValidateCodeException {
        if (org.apache.commons.lang3.StringUtils.isBlank(key)) {
            throw new ValidateCodeException("验证码key不能为空");
        }
        BtoValidateCodeProperties code = properties.getCode();
        setHeader(response, code.getType());
        Captcha captcha = createCaptcha(code);
        redisService.set(BtoConstant.CODE_PREFIX + key, org.apache.commons.lang3.StringUtils.lowerCase(captcha.text()), code.getTime());
        if (BtoConstant.CAPTCHA_TYPE_PICTURE.equals(responseType)) {
            captcha.out(response.getOutputStream());
        } else if (BtoConstant.CAPTCHA_TYPE_NUMBER.equals(responseType)) {
            return ResultVo.success(captcha.text());
        } else {
            return ResultVo.fail(ResultCode.PARAM_ERROR.getMessage());
        }
        return null;
    }

    /**
     * 校验验证码
     *
     * @param key   前端上送 key
     * @param value 前端上送待校验值
     */
    public void check(String key, String value) throws ValidateCodeException {
        Object codeInRedis = redisService.get(BtoConstant.CODE_PREFIX + key);
        if (StringUtils.isBlank(value)) {
            throw new ValidateCodeException("请输入验证码");
        }
        if (codeInRedis == null) {
            throw new ValidateCodeException("验证码已过期");
        }
        if (!StringUtils.equalsIgnoreCase(value, String.valueOf(codeInRedis))) {
            throw new ValidateCodeException("验证码不正确");
        }
    }

    private Captcha createCaptcha(BtoValidateCodeProperties code) {
        Captcha captcha = null;
        if (StringUtils.equalsIgnoreCase(code.getType(), BtoConstant.GIF)) {
            captcha = new GifCaptcha(code.getWidth(), code.getHeight(), code.getLength());
        } else {
            captcha = new SpecCaptcha(code.getWidth(), code.getHeight(), code.getLength());
        }
        captcha.setCharType(code.getCharType());
        return captcha;
    }

    private void setHeader(HttpServletResponse response, String type) {
        if (StringUtils.equalsIgnoreCase(type, BtoConstant.GIF)) {
            response.setContentType(MediaType.IMAGE_GIF_VALUE);
        } else {
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
        }
        response.setHeader(HttpHeaders.PRAGMA, "No-cache");
        response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache");
        response.setDateHeader(HttpHeaders.EXPIRES, 0L);
    }
}