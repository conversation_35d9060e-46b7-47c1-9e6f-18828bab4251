package com.botong.serversystem.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.serverinterface.entity.DeptInterface;
import com.botong.entity.system.Dept;
import com.botong.entity.system.DeptTree;
import com.botong.entity.system.Tree;
import com.botong.entity.system.User;
import com.botong.exception.BtoException;
import com.botong.serversystem.entity.respones.DeptInfoVO;
import com.botong.serversystem.mapper.DeptMapper;
import com.botong.serversystem.service.DeptInterfaceService;
import com.botong.serversystem.service.DeptService;
import com.botong.serversystem.service.UserService;
import com.botong.utils.BaseTreeUtil;
import com.botong.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/7/17.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {

    @Autowired
    private DeptInterfaceService deptInterfaceService;

    private UserService userService;

    public DeptServiceImpl(@Lazy UserService userService) {
        this.userService = userService;
    }

    @Override
    public List<? extends Tree<?>> selectList(String deptName) {
        QueryWrapper<Dept> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StringUtils.isNotBlank(deptName), Dept::getDeptName, deptName);
        queryWrapper.lambda().orderBy(true, false, Dept::getOrderNum);
        List<Dept> deptList = this.baseMapper.selectList(queryWrapper);
        List<DeptTree> trees = new ArrayList<>();
        buildTrees(trees, deptList);
        return BaseTreeUtil.build(trees);
    }

    @Override
    public Dept info(Long deptId) {
        return getById(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDept(Dept dept) {
        if (dept.getParentId() == null) {
            dept.setParentId(Dept.TOP_DEPT_ID);
        }
        Dept exDept = this.lambdaQuery().eq(Dept::getDeptName, dept.getDeptName()).one();
        if (ObjectUtil.isNotNull(exDept)) {
            throw new BtoException("部门名称已存在");
        }

        this.save(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDept(Dept dept) {
        if (dept.getParentId() == null) {
            dept.setParentId(Dept.TOP_DEPT_ID);
        }
        List<Dept> depts = this.lambdaQuery()
                .eq(Dept::getDeptName, dept.getDeptName())
                .ne(Dept::getDeptId, dept.getDeptId())
                .list();
        if (CollUtil.isNotEmpty(depts)) {
            throw new BtoException("部门名称已存在");
        }
        this.baseMapper.updateById(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(List<Long> deptIds) {
        this.delete(deptIds);

    }

    @Override
    public Dept getDeptByName(String name) {
        QueryWrapper<Dept> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(Dept::getDeptName).eq(Dept::getDeptName, name);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<DeptInfoVO> getDeptByInterfaceId(Long interfaceId) {
        LambdaQueryWrapper<DeptInterface> deptInterfaceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deptInterfaceLambdaQueryWrapper.eq(DeptInterface::getInterfaceId, interfaceId);
        List<DeptInterface> deptInterfacesList = deptInterfaceService.list(deptInterfaceLambdaQueryWrapper);
        if (CollUtil.isEmpty(deptInterfacesList)){
            return Collections.emptyList();
        }
        List<Long> deptIds = deptInterfacesList.stream().map(DeptInterface::getDeptId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<Dept> deptLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deptLambdaQueryWrapper.in(CollUtil.isNotEmpty(deptIds), Dept::getDeptId, deptIds);
        List<Dept> deptList = this.list(deptLambdaQueryWrapper);
        if (CollUtil.isEmpty(deptList)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyBeanList(deptList, DeptInfoVO.class);
    }

    private void buildTrees(List<DeptTree> trees, List<Dept> depts) {
        depts.forEach(dept -> {
            DeptTree tree = new DeptTree();
            tree.setId(dept.getDeptId().toString());
            tree.setParentId(dept.getParentId().toString());
            tree.setLabel(dept.getDeptName());
            tree.setOrderNum(dept.getOrderNum());
            trees.add(tree);
        });
    }

    private void delete(List<Long> deptIds) {
        removeByIds(deptIds);
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Dept::getParentId, deptIds);
        // 父级部门删除时删除子部门
        List<Dept> depts = baseMapper.selectList(queryWrapper);
        List<Long> deptIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(depts)) {
            depts.forEach(d -> deptIdList.add(d.getDeptId()));
            this.delete(deptIdList);
        }
        deptIdList.addAll(deptIds);
        // 清空部门对应的接口关系
        deptInterfaceService.lambdaUpdate().in(DeptInterface::getDeptId, deptIds).remove();
        //重置用户部门id
        LambdaUpdateWrapper<User> userUpdateWrapper = new LambdaUpdateWrapper<>();
        userUpdateWrapper.in(User::getDeptId, deptIds)
                .set(User::getDeptId, 0);
        userService.update(userUpdateWrapper);
    }

}
