package com.botong.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.botong.constant.BtoConstant;
import lombok.val;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/7/22.
 */
public class JwtUtil {

    /**
     * 获取当前请求的用户信息
     *
     * @return CurrentUser 当前用户信息
     */
    public static Map<String, Object> getCurrentUserInfo() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new RuntimeException("请求异常，无法获取请求域信息");
        }
        HttpServletRequest request = attributes.getRequest();
        String header = request.getHeader("Authorization");
        String token = StrUtil.subAfter(header, "bearer ", false);
        return  analyzeToken(token);
    }

    /**
     * 获取当前请求的用户id
     *
     * @return String 用户id
     */
    public static Long getCurrentUserId() {
        Object userId = getCurrentUserInfo().get("userId");
        return Convert.toLong(userId);
    }

    public static Map<String, Object> analyzeToken(String token) {
        HashMap<String, Object> hashMap = new HashMap<>();
        val jwtVerifier = JWT.require((Algorithm.HMAC256(BtoConstant.SIGNING_KEY))).build();
        // 解析指定的token
        DecodedJWT decodedToken = jwtVerifier.verify(token);
        Claim claim = decodedToken.getClaim("userInfo");
        Map<String, Object> map = claim.asMap();
        if (null == map){
            Object username = decodedToken.getClaims().get("client_id").asString();
            hashMap.put("username", username);
            return hashMap;
        }
        return map;
    }

}