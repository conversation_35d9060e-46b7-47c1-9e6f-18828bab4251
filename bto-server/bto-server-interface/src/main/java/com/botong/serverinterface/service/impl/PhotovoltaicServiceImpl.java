package com.botong.serverinterface.service.impl;

import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.serverinterface.feign.PhotovoltaicFeign;
import com.botong.serverinterface.pojo.dto.InverterAlarmDTO;
import com.botong.serverinterface.service.PhotovoltaicService;
import com.botong.utils.ObjUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * <AUTHOR> by zhb on 2023/8/8.
 */
@Service
public class PhotovoltaicServiceImpl implements PhotovoltaicService {

    private static final String GINLONG = "LANG";


    @Autowired
    private PhotovoltaicFeign photovoltaicFeign;

    @Override
    public Object getInverterAlarm(InverterAlarmDTO inverterAlarmDTO) {
        Object inverterAlarm = photovoltaicFeign.getInverterAlarm(inverterAlarmDTO);
        if (ResultCode.FEIGN_ERROR.getMessage().equals(inverterAlarm.toString())) {
            return ResultVo.fail(ResultCode.FEIGN_ERROR);
        }
        Object data = ObjUtil.getObjValue(inverterAlarm, "data");
        return ResultVo.success(ObjUtil.getObjValue(data, "list"));
    }

    @Override
    public Object getInverterAlarmDict() {
        ArrayList<String> list = new ArrayList<>();
        // 后续厂商接口可请求，目前暂且一个
        list.add(GINLONG);
        Object inverterAlarm = photovoltaicFeign.getInverterAlarmDict(list);
        if (ResultCode.FEIGN_ERROR.getMessage().equals(inverterAlarm.toString())) {
            return ResultVo.fail(ResultCode.FEIGN_ERROR);
        }
        Object data = ObjUtil.getObjValue(inverterAlarm, "data");
        return ResultVo.success(data);
    }

}
