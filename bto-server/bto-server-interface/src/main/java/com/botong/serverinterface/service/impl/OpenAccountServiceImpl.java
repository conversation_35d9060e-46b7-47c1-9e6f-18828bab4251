package com.botong.serverinterface.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.constant.BtoInterfaceUrl;
import com.botong.entity.serverinterface.entity.OpenAccount;
import com.botong.serverinterface.mapper.OpenAccountMapper;
import com.botong.serverinterface.service.OpenAccountService;
import com.botong.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */

@Service
@Slf4j
public class OpenAccountServiceImpl extends ServiceImpl<OpenAccountMapper, OpenAccount> implements OpenAccountService {

    @Autowired
    private RedisService redisService;


    public void getPhotovoltaicAccessTokenParameter() {
        List<OpenAccount> list = this.list();
        if (!list.isEmpty()) {
            for (OpenAccount openAccount : list) {
                String parameter = JSONUtil.parse(openAccount).toString();
                HttpRequest post = HttpUtil.createPost(BtoInterfaceUrl.PHOTOVOLTAIC_PREFIX_URL + BtoInterfaceUrl.GET_PHOTOVOLTAIC_TOKEN);
                HttpRequest response = post.body(parameter, ContentType.JSON.getValue());
                String result = response.execute().body();
                log.info("请求成功获取第三方token");
                JSONObject jsonObject = JSONObject.parseObject(result);
                Object o = jsonObject.get("status");
                if (o != null && "00000".equals(o.toString())) {
                    Object body = jsonObject.get("data");
                    Map<String, Object> map = Convert.toMap(String.class, Object.class, body);
                    redisService.set(OpenAccount.REDIS_PREFIX + openAccount.getClientId(), map.get("accessToken"));
                    log.info("存储成功 key:" + OpenAccount.REDIS_PREFIX + openAccount.getClientId());
                }

            }
        }

    }

}