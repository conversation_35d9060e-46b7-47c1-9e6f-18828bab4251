package com.botong.serverinterface.pojo.dto;

import com.botong.entity.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlantQueryDTO extends PageDTO {

    private String orderId;

    private String plantUid;

    private String plantName;

    private String province;

    private String city;

    private String area;

    private String address;

    private String inverterSn;

    @ApiModelProperty(value = "项目ID", hidden = true)
    private Integer special;

}