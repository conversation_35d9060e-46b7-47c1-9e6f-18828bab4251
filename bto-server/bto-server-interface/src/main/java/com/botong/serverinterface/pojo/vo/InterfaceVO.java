package com.botong.serverinterface.pojo.vo;

import cn.hutool.core.util.ObjectUtil;
import com.botong.enums.RequestMethodEnum;
import com.botong.enums.StatusEnum;
import com.botong.serverinterface.pojo.dto.InterfaceParamDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/13 11:47
 */
@Data
@AllArgsConstructor
@ToString
@NoArgsConstructor
public class InterfaceVO {
    @ApiModelProperty("接口id")
    private Long interfaceId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("接口描述")
    @Size(max = 85, message = "描述过长，最大长度为85")
    private String interfaceDescribe;
    @ApiModelProperty("接口请求方式，1:post,2:get,3:put,4:delete")
    @JsonFormat
    private RequestMethodEnum requestMethod;
    @ApiModelProperty("接口url")
    private String url;
    @ApiModelProperty("请求头")
    private String requestHeader;
    @ApiModelProperty("响应头")
    private String responseHeader;
    @ApiModelProperty("接口状态，0:失效，1:正常")
    @JsonFormat
    private StatusEnum status;
    @ApiModelProperty("请求参数")
    private List<InterfaceParamDTO> requestParas;

    {
        requestParas = new ArrayList<>();
    }

    public String getStatus() {
        return ObjectUtil.isNotNull(status) ? status.getName() : "UNKNOWN";
    }

    public String getRequestMethod() {
        return ObjectUtil.isNotNull(requestMethod) ? requestMethod.getName() : "UNKNOWN";
    }
}
