package com.botong.oauth.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.router.RouterMeta;
import com.botong.entity.router.VueRouter;
import com.botong.entity.system.Menu;
import com.botong.oauth.mapper.MenuMapper;
import com.botong.utils.BaseTreeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/7/19.
 */
@Slf4j
@Service
public class MenuService extends ServiceImpl<MenuMapper, Menu> {

    public String findUserPermissions(String username) {
        List<Menu> userPermissions = this.baseMapper.findUserPermissions(username);
        return userPermissions.stream().map(Menu::getPerms).collect(Collectors.joining(","));
    }

    public List<Menu> findUserMenus(String username) {
        return this.baseMapper.findUserMenus(username);
    }

    public List<VueRouter<Menu>> getUserRouters(String username) {
        List<VueRouter<Menu>> routes = new ArrayList<>();
        List<Menu> menus = this.findUserMenus(username);
        menus.forEach(menu -> {
            VueRouter<Menu> route = new VueRouter<>();
            route.setId(menu.getMenuId().toString());
            route.setParentId(menu.getParentId().toString());
            route.setPath(menu.getPath());
            route.setRedirect(menu.getRedirect());
            route.setComponent(menu.getComponent());
            route.setName(menu.getMenuName());
            route.setMeta(new RouterMeta(menu.getTitle(), menu.getIcon(), true, route.getHidden()));
            routes.add(route);
        });
        return BaseTreeUtil.buildVueRouter(routes);
    }

}