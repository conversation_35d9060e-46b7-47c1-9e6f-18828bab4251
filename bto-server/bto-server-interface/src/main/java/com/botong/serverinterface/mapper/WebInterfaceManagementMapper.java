package com.botong.serverinterface.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.botong.entity.serverinterface.entity.WebInterfaceManagement;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * web接口管理表(WebInterfaceManagement)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-12 14:15:08
 */
@Mapper
public interface WebInterfaceManagementMapper extends BaseMapper<WebInterfaceManagement> {

    /**
     * 获取接口名称和类别id信息
     *
     * @return {@link List }<{@link InterfaceNameVO }>
     * <AUTHOR>
     * @since 2023-08-05 20:52:16
     */
    List<InterfaceNameVO> getInterfaceName();
}

