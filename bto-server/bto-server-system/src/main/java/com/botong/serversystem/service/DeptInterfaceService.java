package com.botong.serversystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.serverinterface.entity.DeptInterface;
import com.botong.entity.vo.ResultVo;

import java.util.List;


/**
 * (DeptInterface)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-03 14:46:50
 */
public interface DeptInterfaceService extends IService<DeptInterface> {

    /**
     * 根据部门id和接口id添加数据
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id集合
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 15:03:25
     */
    ResultVo insert(Long deptId, List<Long> interfaceIds);

    /**
     * 根据部门id和接口id删除对应数据
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 17:43:19
     */
    ResultVo delete(Long deptId, List<Long> interfaceIds);

    /**
     * 根据部门id和接口id更新对应数据
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 17:43:52
     */
    ResultVo update(Long deptId, List<Long> interfaceIds);

    /**
     * 根据部门id获取接口列表
     *
     * @param deptId 部门id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-03 18:12:43
     */
    List<Long> list(Long deptId);

    /**
     * 按接口删除
     *
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-07 14:20:12
     */
    ResultVo deleteByinterface(List<Long> interfaceIds);
}

