<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.botong.serverinterface.mapper.WebInterfaceManagementMapper">
    <!--    List<WebInterfaceManagement> getInterfaceName();-->
    <select id="getInterfaceName" resultType="com.botong.entity.serverinterface.vo.InterfaceNameVO">
        SELECT t1.interface_id, t1.`name`, t2.class_id
        FROM t_interface_info t1
        LEFT JOIN t_interface_classification t2 ON t1.interface_id = t2.interface_id
    </select>
</mapper>