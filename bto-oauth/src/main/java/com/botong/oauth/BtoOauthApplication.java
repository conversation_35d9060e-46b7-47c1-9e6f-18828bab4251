package com.botong.oauth;

import com.botong.annotation.BtoCloudApplication;
import com.botong.annotation.EnableBtoAuthExceptionHandler;
import com.botong.annotation.EnableBtoLettuceRedis;
import com.botong.annotation.EnableBtoServerProtect;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@SpringBootApplication
@BtoCloudApplication
@MapperScan("com.botong.oauth.mapper")
@EnableBtoLettuceRedis
public class BtoOauthApplication {

    public static void main(String[] args) {
        SpringApplication.run(BtoOauthApplication.class, args);
    }

}
