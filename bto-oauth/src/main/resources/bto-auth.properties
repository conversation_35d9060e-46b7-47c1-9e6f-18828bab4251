bto.auth.accessTokenValiditySeconds=86400
bto.auth.refreshTokenValiditySeconds=604800
bto.auth.clients[0].client=bto
bto.auth.clients[0].secret=123456
bto.auth.clients[0].grantType=password,authorization_code,refresh_token,client_credentials
bto.auth.clients[0].scope=all

bto.auth.swagger.basePackage=com.botong.oauth.controller
bto.auth.swagger.title=\u6570\u636E\u4E2D\u53F0\u7CFB\u7EDF\u5F00\u53D1\u6587\u6863
bto.auth.swagger.description=\u6570\u636E\u4E2D\u53F0\u6388\u6743\u670D\u52A1\u6587\u6863
bto.auth.swagger.version=1.0
bto.auth.swagger.author=ZHB
bto.auth.swagger.url=https://www.btosolarman.com/
bto.auth.swagger.license=Apache 2.0
bto.auth.swagger.licenseUrl=https://www.apache.org/licenses/LICENSE-2.0.html
bto.auth.anonUrl=/captcha,/swagger-ui.html,/webjars/**,/swagger-resources/**,/v2/api-docs/**,/,/csrf,/actuator/**,/doc.html,/v2/api-docs-ext,/swagger-resources/configuration/ui,/swagger-resources/configuration/security,/swagger-resources
bto.auth.swagger.grantUrl=http://localhost:8301/auth/oauth/token
bto.auth.swagger.name=oauth_swagger
bto.auth.swagger.scope=test
bto.auth.swagger.groupName=oauth-api

bto.auth.code.time=120
bto.auth.code.type=png
bto.auth.code.width=115
bto.auth.code.height=42
bto.auth.code.length=4
bto.auth.code.charType=2