package com.botong.serversystem.entity.respones;

import com.baomidou.mybatisplus.annotation.TableField;
import com.botong.entity.system.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> by zhb on 2023/7/17.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SystemUserVo extends User implements Serializable {

    private static final long serialVersionUID = 8127903731970453960L;
    /**
     * 部门名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 角色 ID
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "角色 ID", required = true)
    private Long roleId;

    /**
     * 角色名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "角色名称")
    private String roleName;

}
