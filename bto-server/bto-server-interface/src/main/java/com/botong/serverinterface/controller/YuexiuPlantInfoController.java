package com.botong.serverinterface.controller;


import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.PlantInfoVO;
import com.botong.entity.vo.ResultVo;
import com.botong.serverinterface.pojo.dto.YuexiuPlantQueryDTO;
import com.botong.serverinterface.service.PlantInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.botong.constant.PlantInfoSpecialConstants.YUEXIU_SPECIAL;

/**
 * (YuexiuPlantInfo)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:43
 */
@Api(tags = "越秀电站信息")
@RestController
@RequestMapping("yuexiuPlantInfo")
public class YuexiuPlantInfoController {
    /**
     * 服务对象
     */
    @Resource
    private PlantInfoService plantInfoService;

    /**
     * 分页查询数据
     *
     * @param plantQueryDTO
     * @param currentPage
     * @param size
     * @return
     */

    @ApiOperation("越秀电站信息查询")
    @PostMapping("/page")
    public ResultVo<CommonPage<PlantInfoVO>> queryYuexiuPlantInfoPages(@RequestBody(required = false) YuexiuPlantQueryDTO plantQueryDTO,
                                                                       @RequestParam(required = false, defaultValue = "1", value = "currentPage") Integer currentPage,
                                                                       @RequestParam(required = false, defaultValue = "10", value = "size") Integer size) {
        CommonPage<PlantInfoVO> pageVo = plantInfoService.queryYuexiuPlantInfoPages(plantQueryDTO, currentPage, size, YUEXIU_SPECIAL);
        return ResultVo.success(pageVo);
    }

}

