server:
  port: 8301
spring:
  profiles:
   active: prod
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  application:
    name: BTO-Gateway
  cloud:
    gateway:
      routes:
        - id: BTO-Oauth
          uri: lb://BTO-Oauth
          predicates:
            - Path=/auth/**
          filters:
            - name: Hystrix
              args:
                name: authfallback
                fallbackUri: forward:/fallback/BTO-Oauth
        - id: BTO-Server-System
          uri: lb://BTO-Server-System
          predicates:
            - Path=/system/**
          filters:
            - name: Hystrix
              args:
                name: systemfallback
                fallbackUri: forward:/fallback/BTO-Server-System
        - id: BTO-Server-Interface
          uri: lb://BTO-Server-Interface
          predicates:
            - Path=/interface/**
          filters:
            - name: Hystrix
              args:
                name: interfacefallback
                fallbackUri: forward:/fallback/BTO-Server-Interface
      default-filters:
        - StripPrefix=1

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 100000

ribbon:
  ReadTimeout: 100000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0


management:
  endpoint:
    health:
      show-details: ALWAYS
  endpoints:
    web:
      exposure:
        include: health,info,gateway

info:
  app:
    name: ${spring.application.name}
    description: "@project.description@"
    version: "@project.version@"
