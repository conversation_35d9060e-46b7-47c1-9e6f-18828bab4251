package com.botong.serversystem.properties;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:server-system.properties"})
@ConfigurationProperties(prefix = "server.system")
public class BtoServerSystemProperties {

    /**
     * 免认证 URI，多个值的话以逗号分隔
     */
    private String anonUrl;

    private SwaggerProperties swagger = new SwaggerProperties();
}