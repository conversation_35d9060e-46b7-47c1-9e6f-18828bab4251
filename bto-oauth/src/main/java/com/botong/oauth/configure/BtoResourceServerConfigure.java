package com.botong.oauth.configure;

import com.botong.handler.BtoAccessDeniedHandler;
import com.botong.handler.BtoAuthExceptionEntryPoint;
import com.botong.oauth.properties.BtoAuthProperties;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Configuration
@EnableResourceServer
public class BtoResourceServerConfigure extends ResourceServerConfigurerAdapter {

    @Autowired
    private BtoAccessDeniedHandler accessDeniedHandler;
    @Autowired
    private BtoAuthExceptionEntryPoint exceptionEntryPoint;

    @Autowired
    private BtoAuthProperties properties;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        String[] anonUrls = StringUtils.splitByWholeSeparatorPreserveAllTokens(properties.getAnonUrl(), ",");

        http.csrf().disable()
                .requestMatchers().antMatchers("/**")
                .and()
                .authorizeRequests()
                .antMatchers(anonUrls).permitAll()
                .antMatchers("/**").authenticated();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {
        resources.authenticationEntryPoint(exceptionEntryPoint)
                .accessDeniedHandler(accessDeniedHandler);
    }
}