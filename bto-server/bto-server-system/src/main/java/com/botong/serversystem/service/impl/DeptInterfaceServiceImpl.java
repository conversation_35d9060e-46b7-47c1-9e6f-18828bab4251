package com.botong.serversystem.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.serverinterface.entity.DeptInterface;
import com.botong.entity.vo.ResultVo;
import com.botong.serversystem.entity.respones.DeptInterfaceVO;
import com.botong.serversystem.mapper.DeptInterfaceMapper;
import com.botong.serversystem.service.DeptInterfaceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (DeptInterface)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-03 14:46:50
 */
@Transactional(rollbackFor = Exception.class)
@Service("deptInterfaceService")
public class DeptInterfaceServiceImpl extends ServiceImpl<DeptInterfaceMapper, DeptInterface> implements DeptInterfaceService {

    @Override
    public ResultVo insert(Long deptId, List<Long> interfaceIds) {
        List<DeptInterface> deptInterfaces = interfaceIds.stream()
                .map(id -> DeptInterface.builder().deptId(deptId).interfaceId(id).build())
                .collect(Collectors.toList());
        boolean success = this.saveBatch(deptInterfaces);
        return success ? ResultVo.success() : ResultVo.fail();
    }

    @Override
    public ResultVo delete(Long deptId, List<Long> interfaceIds) {
        LambdaQueryWrapper<DeptInterface> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeptInterface::getDeptId, deptId);
        queryWrapper.in(DeptInterface::getInterfaceId, interfaceIds);
        boolean success = this.remove(queryWrapper);
        return success ? ResultVo.success("操作成功！") : ResultVo.fail("操作失败或没有匹配的记录！");
    }

    @Override
    public ResultVo update(Long deptId, List<Long> interfaceIds) {
        // 清空部门对应的接口
        this.lambdaUpdate().eq(DeptInterface::getDeptId, deptId).remove();
        // 插入新的deptId和interfaceIds对应的记录
        HashSet<Long> newInterfaceIds = new HashSet<>(interfaceIds);
        List<DeptInterface> deptInterfaces = newInterfaceIds.stream()
                .map(id -> DeptInterface.builder().deptId(deptId).interfaceId(id).build())
                .collect(Collectors.toList());
        boolean success = this.saveBatch(deptInterfaces);
        return success ? ResultVo.success() : ResultVo.fail();
    }

    @Override
    public List<Long> list(Long deptId) {
        List<DeptInterfaceVO> deptInterfaceList = baseMapper.listByDeptId(deptId);
        return deptInterfaceList.stream().map(DeptInterfaceVO::getInterfaceId).distinct().collect(Collectors.toList());
    }

    @Override
    public ResultVo deleteByinterface(List<Long> interfaceIds) {
        boolean success = this.lambdaUpdate().in(DeptInterface::getInterfaceId, interfaceIds).remove();
        if (success){
            return ResultVo.success();
        }
        return ResultVo.fail();
    }
}

