package com.botong.oauth.configure;

import com.botong.constant.BtoConstant;
import com.botong.oauth.enhancer.BtoTokenEnhancer;
import com.botong.oauth.properties.BtoClientsProperties;
import com.botong.oauth.properties.BtoAuthProperties;
import com.botong.oauth.service.BtoUserDetailServiceImpl;
import com.botong.oauth.translator.BtoWebResponseExceptionTranslator;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.builders.InMemoryClientDetailsServiceBuilder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.provider.token.*;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import java.util.Arrays;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Configuration
@EnableAuthorizationServer
public class BtoAuthorizationServerConfigure extends AuthorizationServerConfigurerAdapter {
    private static final String YUE_XIU_CLIENT_ID = "yuexiu";
    private static final String YUE_XIU_CLIENT_SECRET = "yuexiu..";

    private static final String FENG_DIAN_CLIENT_ID = "fengdian";
    private static final String FENG_DIAN_CLIENT_SECRET = "fengdian..";

    private static final String ZHONG_SHAN_CLIENT_ID = "zsgyzhny";
    private static final String ZHONG_SHAN_CLIENT_SECRET = "zsgyzhny..";

    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;
    @Autowired
    private BtoUserDetailServiceImpl userDetailService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private BtoAuthProperties authProperties;

    @Autowired
    private BtoTokenEnhancer tokenEnhancer;

    @Autowired
    private BtoWebResponseExceptionTranslator exceptionTranslator;

    public TokenEnhancerChain tokenEnhancer() {
        TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
        tokenEnhancerChain.setTokenEnhancers(Arrays.asList(tokenEnhancer, jwtAccessTokenConverter()));
        return tokenEnhancerChain;
    }


    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter accessTokenConverter = new JwtAccessTokenConverter();
        DefaultAccessTokenConverter defaultAccessTokenConverter = (DefaultAccessTokenConverter) accessTokenConverter.getAccessTokenConverter();
        DefaultUserAuthenticationConverter userAuthenticationConverter = new DefaultUserAuthenticationConverter();
        userAuthenticationConverter.setUserDetailsService(userDetailService);
        defaultAccessTokenConverter.setUserTokenConverter(userAuthenticationConverter);
        accessTokenConverter.setSigningKey(BtoConstant.SIGNING_KEY);
        return accessTokenConverter;
    }

    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        InMemoryClientDetailsServiceBuilder builder = clients.inMemory();
        BtoClientsProperties[] clientsArray = authProperties.getClients();
        if (ArrayUtils.isNotEmpty(clientsArray)) {
            for (BtoClientsProperties client : clientsArray) {
                if (StringUtils.isBlank(client.getClient())) {
                    throw new Exception("client不能为空");
                }
                if (StringUtils.isBlank(client.getSecret())) {
                    throw new Exception("secret不能为空");
                }
                String[] grantTypes = StringUtils.splitByWholeSeparatorPreserveAllTokens(client.getGrantType(), ",");
                builder.withClient(client.getClient())
                        .secret(passwordEncoder.encode(client.getSecret()))
                        .authorizedGrantTypes(grantTypes)
                        .scopes(client.getScope())
                        // 设置过期时间
                        .accessTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                        .refreshTokenValiditySeconds(authProperties.getRefreshTokenValiditySeconds())
                        .and()
                        .withClient(YUE_XIU_CLIENT_ID)
                        .secret(passwordEncoder.encode(YUE_XIU_CLIENT_SECRET))
                        .scopes(client.getScope())
                        .accessTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                        .refreshTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                        .and()
                        .withClient(FENG_DIAN_CLIENT_ID)
                        .secret(passwordEncoder.encode(FENG_DIAN_CLIENT_SECRET))
                        .scopes(client.getScope())
                        .accessTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                        .refreshTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                        .and()
                        .withClient(ZHONG_SHAN_CLIENT_ID)
                        .secret(passwordEncoder.encode(ZHONG_SHAN_CLIENT_SECRET))
                        .scopes(client.getScope())
                        .accessTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                        .refreshTokenValiditySeconds(authProperties.getAccessTokenValiditySeconds())
                ;
            }
        }
    }

    @Override
    @SuppressWarnings("all")
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {
        endpoints.tokenStore(tokenStore())
                .tokenStore(redisTokenStore())
                .tokenEnhancer(tokenEnhancer())
                .reuseRefreshTokens(true)
                .accessTokenConverter(jwtAccessTokenConverter())
                .userDetailsService(userDetailService)
                .authenticationManager(authenticationManager)
                .exceptionTranslator(exceptionTranslator);
    }


    @Bean
    public TokenStore tokenStore() {
        return new JwtTokenStore(jwtAccessTokenConverter());
    }

    @Bean
    public TokenStore redisTokenStore() {
        return new RedisTokenStore(redisConnectionFactory);
    }


}