package com.botong.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import com.botong.annotation.IsMobile;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Data
@TableName("t_user")
@ApiModel
public class User implements Serializable {

    private static final long serialVersionUID = -4352868070794165001L;

    /**
     * 用户状态：有效
     */
    public static final String STATUS_VALID = "1";
    /**
     * 用户状态：锁定
     */
    public static final String STATUS_LOCK = "0";
    /**
     * 默认头像
     */
    public static final String DEFAULT_AVATAR = "default.jpg";
    /**
     * 默认密码
     */
    public static final String DEFAULT_PASSWORD = "12345678";
    /**
     * 性别男
     */
    public static final String SEX_MALE = "0";
    /**
     * 性别女
     */
    public static final String SEX_FEMALE = "1";
    /**
     * 性别保密
     */
    public static final String SEX_UNKNOW = "2";

    /**
     * 用户 ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    @ApiModelProperty(value = "用户 ID")
    private Long userId;

    /**
     * 用户名
     */
    @TableField("username")
    @Size(min = 4, max = 10, message = "{range}")
    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    /**
     * 部门 ID
     */
    @TableField("dept_id")
    @ApiModelProperty(value = "部门 ID")
    private Long deptId;

    /**
     * 邮箱
     */
    @TableField("email")
    @Size(max = 50, message = "{noMoreThan}")
    @Email(message = "{email}")
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 联系电话
     */
    @TableField("mobile")
    @IsMobile(message = "{mobile}")
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 状态 0锁定 1有效
     */
    @TableField("status")
    @NotBlank(message = "{required}")
    @ApiModelProperty(value = "状态 0锁定 1有效", required = true)
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间", hidden = true)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间", hidden = true)
    private LocalDateTime modifyTime;

    /**
     * 最近访问时间
     */
    @TableField("last_login_time")
    @ApiModelProperty(value = "最近访问时间")
    private LocalDateTime lastLoginTime;

    /**
     * 性别 0男 1女 2 保密
     */
    @TableField("gender")
    @ApiModelProperty(value = "性别 0男 1女 2 保密")
    private String sex;

    /**
     * 头像
     */
    @TableField("avatar")
    @ApiModelProperty(value = "头像")
    private String avatar;

    /**
     * 描述
     */
    @TableField("description")
    @ApiModelProperty(value = "描述")
    private String description;
}