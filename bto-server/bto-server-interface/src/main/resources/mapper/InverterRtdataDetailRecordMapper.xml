<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.serverinterface.mapper.InverterRtdataDetailRecordMapper">


    <select id="getSumByInverterIds" resultType="com.botong.entity.serverinterface.entity.InverterRtdataDetailRecord">
        SELECT
        'R6I2253J2332C25221' AS inverterId,
        -- 仅聚合 R6I 的 inverter_total_energy
        ROUND(SUM(inverter_total_energy), 3) AS inverter_total_energy,
        -- 其他字段排除 R6I，仅聚合其他三个逆变器
        ROUND(SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN inverter_year_energy ELSE 0 END), 3) AS inverter_year_energy,
        ROUND(SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN inverter_month_energy ELSE 0 END), 3) AS inverter_month_energy,
        ROUND(SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN inverter_today_energy ELSE 0 END), 3) AS inverter_today_energy,
        ROUND(SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN inverter_power ELSE 0 END), 3) AS inverter_power,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN inverter_qpower ELSE 0 END) AS inverter_qpower,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN inverter_pf ELSE 0 END) AS inverter_pf,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l1_volt ELSE 0 END) AS l1_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l1_curr ELSE 0 END) AS l1_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l1_freq ELSE 0 END) AS l1_freq,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l1_dci ELSE 0 END) AS l1_dci,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l1_power ELSE 0 END) AS l1_power,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l1_pf ELSE 0 END) AS l1_pf,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l2_volt ELSE 0 END) AS l2_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l2_curr ELSE 0 END) AS l2_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l2_freq ELSE 0 END) AS l2_freq,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l2_dci ELSE 0 END) AS l2_dci,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l2_power ELSE 0 END) AS l2_power,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l2_pf ELSE 0 END) AS l2_pf,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l3_volt ELSE 0 END) AS l3_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l3_curr ELSE 0 END) AS l3_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l3_freq ELSE 0 END) AS l3_freq,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l3_dci ELSE 0 END) AS l3_dci,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l3_power ELSE 0 END) AS l3_power,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN l3_pf ELSE 0 END) AS l3_pf,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv1_volt ELSE 0 END) AS pv1_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv1_curr ELSE 0 END) AS pv1_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv2_volt ELSE 0 END) AS pv2_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv2_curr ELSE 0 END) AS pv2_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv3_volt ELSE 0 END) AS pv3_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv3_curr ELSE 0 END) AS pv3_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv4_volt ELSE 0 END) AS pv4_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv4_curr ELSE 0 END) AS pv4_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv5_volt ELSE 0 END) AS pv5_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv5_curr ELSE 0 END) AS pv5_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv6_volt ELSE 0 END) AS pv6_volt,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN pv6_curr ELSE 0 END) AS pv6_curr,
        SUM(CASE WHEN inverter_id != 'R6I2253J2332C25221' THEN cavity_temp ELSE 0 END) / 3 AS cavity_temp
        FROM
        v_inverter_rtdata_detail_record
        WHERE
        inverter_id IN ('R5X2802J2446C06443', 'R5X2802J2446C06445', 'R5X2802J2446C06448', 'R6I2253J2332C25221');
    </select>
</mapper>