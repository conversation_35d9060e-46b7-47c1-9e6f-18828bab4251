package com.botong.serverinterface;

import com.botong.annotation.BtoCloudApplication;
import com.botong.annotation.EnableBtoLettuceRedis;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@SpringBootApplication
@ComponentScan({"com.botong.**"})
@EnableGlobalMethodSecurity(prePostEnabled = true)
@EnableBtoLettuceRedis
@BtoCloudApplication
@EnableFeignClients("com.botong.**")
@MapperScan("com.botong.serverinterface.mapper")
public class BtoServerInterfaceApplication {
    public static void main(String[] args) {
        SpringApplication.run(BtoServerInterfaceApplication.class, args);
    }

}
