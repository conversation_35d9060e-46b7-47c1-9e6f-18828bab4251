package com.botong.serverinterface.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.botong.entity.CommonPage;
import com.botong.entity.dto.SortType;
import com.botong.entity.serverinterface.dto.RoleQueryDTO;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.exception.BtoException;
import com.botong.serverinterface.pojo.dto.AddInterfaceDTO;
import com.botong.serverinterface.pojo.dto.InterfaceChangStatusDTO;
import com.botong.serverinterface.pojo.dto.InterfaceDTO;
import com.botong.serverinterface.pojo.dto.InterfaceQueryDTO;
import com.botong.serverinterface.pojo.vo.InterfaceVO;
import com.botong.serverinterface.pojo.vo.RoleInterfaceTree;
import com.botong.serverinterface.service.WebInterfaceManagementService;
import com.botong.utils.JwtUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

import static com.botong.constant.UserRoleConstant.ADMIN_ROLE_ID;

/**
 * web接口管理表(WebInterfaceManagement)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-12 14:15:06
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/webInterfaceManagement")
@Api(tags = "接口管理模块")
public class WebInterfaceManagementController {

    @Resource
    private WebInterfaceManagementService webInterfaceManagementService;

    /**
     * 分页查询接口数据
     *
     * @param interfaceQueryDTO
     * @param currentPage
     * @param size
     * @return
     */
    @ApiOperation("接口数据查询")
    @PostMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentPage", value = "当前页", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", paramType = "query"),
            @ApiImplicitParam(name = "sortField", value = "排序字段1：create_time 2：modify_time", defaultValue = "1", paramType = "query"),
            @ApiImplicitParam(name = "isAsc", value = "是否升序1：true 2：false", defaultValue = "2", paramType = "query"),
    })
    public ResultVo<CommonPage<InterfaceVO>> queryInterfaceDataInPages(
            @RequestBody(required = false) InterfaceQueryDTO interfaceQueryDTO,
            @RequestParam(required = false, defaultValue = "1", value = "currentPage") Integer currentPage,
            @RequestParam(required = false, defaultValue = "10", value = "size") Integer size,
            @RequestParam(required = false, defaultValue = "1") String sortField,
            @RequestParam(required = false, defaultValue = "2") String isAsc,
            @RequestParam(required = false) Long classId
    ) {
        SortType sortType = new SortType(sortField, isAsc);
        Long deptId = Convert.toLong(JwtUtil.getCurrentUserInfo().get("deptId"));
        Long roleId = Convert.toLong(JwtUtil.getCurrentUserInfo().get("roleId"));
        if (Optional.ofNullable(deptId).map(id -> id == 0L).orElse(false) &&!ADMIN_ROLE_ID.equals(roleId)) {
            throw new BtoException("请先绑定部门！");
        }
        // if (ADMIN_ROLE_ID.equals(roleId) && ObjectUtil.isNull(deptId)){
        if (ADMIN_ROLE_ID.equals(roleId)){
            deptId = null;
        }
        CommonPage<InterfaceVO> pageVo = webInterfaceManagementService.queryInterfaceDataInPages(interfaceQueryDTO, currentPage, size, sortType, deptId, classId);
        return ResultVo.success(pageVo);

    }


    /**
     * 新增接口
     *
     * @param interfaceDTO
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping
    public ResultVo insert(@RequestBody @Valid AddInterfaceDTO interfaceDTO) {
        return webInterfaceManagementService.addInterface(interfaceDTO);
    }

    /**
     * 根据id修改接口
     *
     * @param interfaceDTO
     * @return
     */
    @ApiOperation("根据id修改接口")
    @PutMapping
    public ResultVo update(@RequestBody @Valid InterfaceDTO interfaceDTO) {
        return webInterfaceManagementService.updateByInterface(interfaceDTO);
    }

    /**
     * 根据id删除接口
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @ApiOperation("根据id删除接口")
    @DeleteMapping
    public ResultVo delete(@RequestBody List<Long> ids) {
        Optional.ofNullable(ids)
                .filter(list -> !list.isEmpty())
                .orElseThrow(() -> new BtoException(ResultCode.PARAM_ERROR));

        return webInterfaceManagementService.deleteByInterface(ids);
    }

    @ApiOperation("根据id修改接口状态")
    @PutMapping("/changeStatus")
    public ResultVo changeStatusById(@RequestBody InterfaceChangStatusDTO interfaceChangStatusDTO) {
        Optional.ofNullable(interfaceChangStatusDTO)
                .orElseThrow(() -> new BtoException(ResultCode.PARAM_ERROR));
        return webInterfaceManagementService.changeStatusById(interfaceChangStatusDTO);
    }

    @ApiOperation("给角色添加接口")
    @PostMapping("/addInterfaceToRole")
    public ResultVo addInterfaceToRole(@ApiParam(value = "角色id", required = true) @RequestParam Long roleId,
                                       @ApiParam(value = "接口id列表", required = true) @RequestBody List<Long> interfaceIds) {
        if (roleId == null || CollUtil.isEmpty(interfaceIds)) {
            throw new BtoException("roleId或interfaceId不能为空");
        }
        return webInterfaceManagementService.addInterfaceToRole(roleId, interfaceIds);
    }


    @ApiOperation("角色接口树")
    @PostMapping("/roleInterfaceTree")
    public ResultVo<List<RoleInterfaceTree>> roleInterfaceTree(@RequestBody RoleQueryDTO roleQueryDTO) {
        return webInterfaceManagementService.roleInterfaceTree(roleQueryDTO);
    }

    @ApiOperation("所有接口name(不重复)")
    @PostMapping("/getInterfaceInfo")
    public ResultVo<List<InterfaceNameVO>> getInterfaceName() {
        return webInterfaceManagementService.getInterfaceName();
    }

    @ApiOperation("所有接口name(重复)")
    @PostMapping("/getInterfaceInfoRepeat")
    public ResultVo<List<InterfaceNameVO>> getInterfaceInfoRepeat() {
        return webInterfaceManagementService.getInterfaceInfoRepeat();
    }

}

