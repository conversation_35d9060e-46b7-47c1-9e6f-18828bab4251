<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.oauth.mapper.MenuMapper">
    <select id="findUserPermissions" resultType="menu">
        select distinct m.perms
        from t_role r
                 left join t_user_role ur on (r.role_id = ur.role_id)
                 left join t_user u on (u.user_id = ur.user_id)
                 left join t_role_menu rm on (rm.role_id = r.role_id)
                 left join t_menu m on (m.menu_id = rm.menu_id)
        where u.username = #{userName}
          and m.perms is not null
          and m.perms &lt;&gt; ''
    </select>
    <select id="findUserMenus" resultType="menu">
        select m.*
        from t_menu m
        where m.type &lt;&gt; 1
          and m.MENU_ID in
              (select distinct rm.menu_id
               from t_role_menu rm
                        left join t_role r on (rm.role_id = r.role_id)
                        left join t_user_role ur on (ur.role_id = r.role_id)
                        left join t_user u on (u.user_id = ur.user_id)
               where u.username = #{userName})
        order by m.order_num
    </select>
</mapper>