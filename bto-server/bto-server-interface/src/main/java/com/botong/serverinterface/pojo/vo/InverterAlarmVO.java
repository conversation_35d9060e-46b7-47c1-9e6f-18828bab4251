package com.botong.serverinterface.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> by zhb on 2023/8/5.
 */

@Data
@ApiModel
public class InverterAlarmVO {
    @ApiModelProperty(value = "电站编号")
    private String plantUid;
    @ApiModelProperty(value = "电站名称")
    private String plantName;
    @ApiModelProperty(value = "逆变器SN")
    private String inverterId;
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;
    @ApiModelProperty("告警信息")
    private String alarmInfo;
    @ApiModelProperty("告警码")
    private String alarmCode;
    @ApiModelProperty("级别")
    private String alarmLevel;
    @ApiModelProperty("状态（0未处理，1已处理 , 2失效）")
    private String status;
}
