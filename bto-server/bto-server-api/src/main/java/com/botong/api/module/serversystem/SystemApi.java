package com.botong.api.module.serversystem;

import com.botong.constant.BtoServerConstant;
import com.botong.entity.serverinterface.dto.RoleQueryDTO;
import com.botong.entity.serverinterface.vo.RoleNameVO;
import com.botong.entity.vo.ResultVo;
import com.botong.api.fallback.SystemServiceFallback;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@FeignClient(value = BtoServerConstant.BTO_SERVER_SYSTEM, contextId = "systemServiceClient", fallbackFactory = SystemServiceFallback.class)
public interface SystemApi {

    /**
     * Feign调用测试
     *
     * @param name 测试参数
     * @return 测试返回数据
     */
    @GetMapping("system")
    String system(@RequestParam String name);

    /**
     * 获取角色名和接口名
     * @param roleQueryDTO
     * @return
     */
    @PostMapping("role/getRoleNameList")
    ResultVo<List<RoleNameVO>> getRoleNameList(@RequestBody RoleQueryDTO roleQueryDTO);

    /**
     * 根据部门id获取接口id列表
     *
     * @param deptId 部门id
     * @return {@link ResultVo }<{@link List }<{@link Long }>>
     * <AUTHOR>
     * @since 2023-08-04 16:58:56
     */
    @ApiOperation("根据部门id获取接口id集合")
    @GetMapping("deptInterface/list/interfaceIds")
    ResultVo<List<Long>> list(@ApiParam(value = "部门id", required = false) @RequestParam(required = false) Long deptId);


    /**
     * 根据classid获取接口id集合
     *
     * @param classId 类id
     * @return {@link ResultVo }<{@link List }<{@link Long }>>
     * <AUTHOR>
     * @since 2023-08-05 20:52:06
     */
    @ApiOperation("根据classid获取接口id集合")
    @PostMapping("interfaceClass/listByClass")
    ResultVo<List<Long>> listByClass(@RequestParam Long classId);


    /**
     * 按接口删除部门接口
     *
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-07 14:34:15
     */
    @ApiOperation("根据接口id删除 部门接口关联")
    @DeleteMapping("deptInterface/deleteByinterface")
    public ResultVo deleteDeptInterfaceByinterface(@ApiParam(value = "部门id", required = true) @RequestBody List<Long> interfaceIds);


    /**
     * 按接口删除接口类
     *
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-07 14:34:16
     */
    @ApiOperation("根据接口id删除 接口分类关联")
    @DeleteMapping("interfaceClass/deleteByinterface")
    ResultVo deleteInterfaceClassByinterface(@ApiParam(value = "部门id", required = true) @RequestBody List<Long> interfaceIds);

    /**
     * 根据部门id添加接口
     *
     * @param deptId       部门id
     * @param interfaceIds 接口id
     * @return {@link ResultVo }
     * <AUTHOR>
     * @since 2023-08-08 09:05:49
     */
    @ApiOperation("根据部门id添加接口")
    @PostMapping("deptInterface")
    ResultVo insert(@ApiParam(value = "部门id", required = true) @RequestParam Long deptId,
                           @ApiParam(value = "接口id列表", required = true) @RequestBody List<Long> interfaceIds);
}