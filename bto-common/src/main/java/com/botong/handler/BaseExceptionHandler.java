package com.botong.handler;

import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.exception.BtoAuthException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 全局异常处理器
 *
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Slf4j
public class BaseExceptionHandler {

    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResultVo<Object> handleException(Exception e) {
        log.error("系统内部异常，异常信息", e);
        return ResultVo.fail(e.getMessage());
    }

    @ExceptionHandler(value = BtoAuthException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResultVo<Object> handleBtoAuthException(BtoAuthException e) {
        log.error("系统错误", e);
        return ResultVo.fail(e.getMessage());
    }

    @ExceptionHandler(value = AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResultVo<Object> handleAccessDeniedException() {
        return ResultVo.fail(ResultCode.NO_PERMISSION);
    }
}