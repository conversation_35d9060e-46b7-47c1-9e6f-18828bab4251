package com.botong.gateway.properties;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:bto-gateway.properties"})
@ConfigurationProperties(prefix = "bto.gateway")
public class BtoGatewayProperties {
    /**
     * 禁止外部访问的 URI，多个值的话以逗号分隔
     */
    private String forbidRequestUri;
}