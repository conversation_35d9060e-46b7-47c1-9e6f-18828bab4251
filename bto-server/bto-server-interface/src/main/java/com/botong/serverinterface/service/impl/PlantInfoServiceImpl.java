package com.botong.serverinterface.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.PlantInfoVO;
import com.botong.entity.serverinterface.vo.InverterDayElectricityVO;
import com.botong.enums.PowerDistributorEnum;
import com.botong.serverinterface.mapper.PlantInfoMapper;
import com.botong.serverinterface.pojo.dto.*;
import com.botong.serverinterface.pojo.vo.InverterAlarmVO;
import com.botong.serverinterface.pojo.vo.WorkOrderVO;
import com.botong.serverinterface.service.InverterDayElectricityService;
import com.botong.serverinterface.service.PlantInfoService;
import com.botong.utils.BeanCopyUtils;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (YuexiuPlantInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:43
 */
@DS("clickhouse")
@Service("yuexiuPlantInfoService")
public class PlantInfoServiceImpl extends ServiceImpl<PlantInfoMapper, PlantInfoVO> implements PlantInfoService {

    @Autowired
    private PlantInfoMapper plantInfoMapper;
    @Autowired
    private InverterDayElectricityService inverterDayMidService;

    @Override
    public CommonPage<PlantInfoVO> queryYuexiuPlantInfoPages(YuexiuPlantQueryDTO plantQueryDTO, Integer currentPage, Integer size, Integer specialId) {
        LambdaQueryWrapper<PlantInfoVO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlantInfoVO::getSpecial, specialId);
        if (ObjectUtil.isNotNull(plantQueryDTO)) {
            PlantInfoVO plantInfoVO = BeanCopyUtils.copyBean(plantQueryDTO, PlantInfoVO.class);
            queryWrapper
                    .like(StrUtil.isNotBlank(plantQueryDTO.getOrderId()), PlantInfoVO::getOrderId, plantQueryDTO.getOrderId())
                    .like(StrUtil.isNotBlank(plantQueryDTO.getPlantUid()), PlantInfoVO::getPlantUid, plantQueryDTO.getPlantUid())
                    .like(StrUtil.isNotBlank(plantInfoVO.getPlantUid()), PlantInfoVO::getPlantUid, plantQueryDTO.getPlantUid())
                    .like(StrUtil.isNotBlank(plantInfoVO.getPlantName()), PlantInfoVO::getPlantName, plantQueryDTO.getPlantName())
                    .like(StrUtil.isNotBlank(plantInfoVO.getProvince()), PlantInfoVO::getProvince, plantQueryDTO.getProvince())
                    .like(StrUtil.isNotBlank(plantInfoVO.getCity()), PlantInfoVO::getCity, plantQueryDTO.getCity())
                    .like(StrUtil.isNotBlank(plantInfoVO.getArea()), PlantInfoVO::getArea, plantQueryDTO.getArea())
                    .like(StrUtil.isNotBlank(plantInfoVO.getAddress()), PlantInfoVO::getAddress, plantQueryDTO.getAddress())
                    .like(StrUtil.isNotBlank(plantInfoVO.getInverterSn()), PlantInfoVO::getInverterSn, plantQueryDTO.getInverterSn())
                    .orderByDesc(PlantInfoVO::getCreateTime);
        }
        Page<PlantInfoVO> page = new Page<>(currentPage, size);

        this.page(page, queryWrapper);
        CommonPage<PlantInfoVO> plantInfoCommonPage = CommonPage.pageInfo(page, page.getRecords());

        return plantInfoCommonPage;
    }

    @Override
    public CommonPage<PlantInfoVO> getPlantInfoPage(PlantQueryDTO plantQueryDTO) {
        LambdaQueryWrapper<PlantInfoVO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlantInfoVO::getSpecial, plantQueryDTO.getSpecial());
        if (ObjectUtil.isNotNull(plantQueryDTO)) {
            PlantInfoVO plantInfoVO = BeanCopyUtils.copyBean(plantQueryDTO, PlantInfoVO.class);
            queryWrapper
                    .like(StrUtil.isNotBlank(plantQueryDTO.getOrderId()), PlantInfoVO::getOrderId, plantQueryDTO.getOrderId())
                    .like(StrUtil.isNotBlank(plantQueryDTO.getPlantUid()), PlantInfoVO::getPlantUid, plantQueryDTO.getPlantUid())
                    .like(StrUtil.isNotBlank(plantInfoVO.getPlantUid()), PlantInfoVO::getPlantUid, plantQueryDTO.getPlantUid())
                    .like(StrUtil.isNotBlank(plantInfoVO.getPlantName()), PlantInfoVO::getPlantName, plantQueryDTO.getPlantName())
                    .like(StrUtil.isNotBlank(plantInfoVO.getProvince()), PlantInfoVO::getProvince, plantQueryDTO.getProvince())
                    .like(StrUtil.isNotBlank(plantInfoVO.getCity()), PlantInfoVO::getCity, plantQueryDTO.getCity())
                    .like(StrUtil.isNotBlank(plantInfoVO.getArea()), PlantInfoVO::getArea, plantQueryDTO.getArea())
                    .like(StrUtil.isNotBlank(plantInfoVO.getAddress()), PlantInfoVO::getAddress, plantQueryDTO.getAddress())
                    .like(StrUtil.isNotBlank(plantInfoVO.getInverterSn()), PlantInfoVO::getInverterSn, plantQueryDTO.getInverterSn())
                    .orderByDesc(PlantInfoVO::getCreateTime);
        }
        Page<PlantInfoVO> page = new Page<>(plantQueryDTO.getPageNum(), plantQueryDTO.getPageSize());
        this.page(page, queryWrapper);
        page.getRecords().forEach(item -> {
            item.setPowerDistributor(PowerDistributorEnum.getNameByCode(item.getPowerDistributor()));
        });
        return CommonPage.pageInfo(page, page.getRecords());
    }

    @Override
    public CommonPage<WorkOrderVO> getWorkOrderPage(WorkOrderDTO query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<WorkOrderVO> list = plantInfoMapper.getWorkOrderPage(query);
        return CommonPage.pageInfo(list);
    }

    @Override
    @DS("clickhouse")
    public CommonPage<InverterAlarmVO> getInverterAlarm(InverterAlarmDTO inverterAlarmDTO) {
        PageHelper.startPage(inverterAlarmDTO.getPageNum(), inverterAlarmDTO.getPageSize());
        List<InverterAlarmVO> list = plantInfoMapper.getInverterAlarm(inverterAlarmDTO);
        return CommonPage.pageInfo(list);
    }

    @Override
    @DS("clickhouse")
    public CommonPage<InverterDayElectricityVO> getHistoryElectricity(InverterDayElectricityDTO inverterDayMidDTO) {
        PageHelper.startPage(inverterDayMidDTO.getPageNum(), inverterDayMidDTO.getPageSize());

        List<InverterDayElectricityVO> list = plantInfoMapper.getHistoryElectricity(inverterDayMidDTO);
        return CommonPage.pageInfo(list);
    }
}

