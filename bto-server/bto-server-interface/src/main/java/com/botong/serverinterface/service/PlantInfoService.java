package com.botong.serverinterface.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.botong.entity.CommonPage;
import com.botong.entity.serverinterface.entity.PlantInfoVO;
import com.botong.entity.serverinterface.vo.InverterDayElectricityVO;
import com.botong.serverinterface.pojo.dto.*;
import com.botong.serverinterface.pojo.vo.InverterAlarmVO;
import com.botong.serverinterface.pojo.vo.WorkOrderVO;


/**
 * (YuexiuPlantInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:43
 */
public interface PlantInfoService extends IService<PlantInfoVO> {

    /**
     * 越秀电站信息分页查询
     *
     * @param plantQueryDTO
     * @param currentPage
     * @param size
     * @param specialId
     * @return
     */
    CommonPage<PlantInfoVO> queryYuexiuPlantInfoPages(YuexiuPlantQueryDTO plantQueryDTO, Integer currentPage, Integer size, Integer specialId);

    CommonPage<PlantInfoVO> getPlantInfoPage(PlantQueryDTO plantQueryDTO);

    CommonPage<WorkOrderVO> getWorkOrderPage(WorkOrderDTO query);

    CommonPage<InverterAlarmVO> getInverterAlarm(InverterAlarmDTO inverterAlarmDTO);

    CommonPage<InverterDayElectricityVO> getHistoryElectricity(InverterDayElectricityDTO inverterDayMidDTO);
}