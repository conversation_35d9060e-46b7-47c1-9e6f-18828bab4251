package com.botong.handler;

import com.botong.constant.BtoConstant;
import com.botong.entity.vo.ResultVo;
import com.botong.enums.ResultCode;
import com.botong.utils.ResponseUtil;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class BtoAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException {
        ResponseUtil.makeResponse(response, BtoConstant.APPLICATION_JSON_UTF8_VALUE, HttpServletResponse.SC_FORBIDDEN, ResultVo.fail(ResultCode.NO_PERMISSION));
    }
}