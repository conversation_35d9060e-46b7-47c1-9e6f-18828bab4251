server:
  port: 8201

spring:
  application:
    name: BTO-Server-System
  profiles:
   active: prod

security:
  oauth2:
    resource:
      id: ${spring.application.name}
      user-info-uri: http://localhost:8301/auth/user

mybatis-plus:
  #实体类配置别名，默认为类名首字母小写
  type-aliases-package: com.botong.entity
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: null
  global-config:
    banner: false
info:
  app:
    name: ${spring.application.name}
    description: "@project.description@"
    version: "@project.version@"
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS