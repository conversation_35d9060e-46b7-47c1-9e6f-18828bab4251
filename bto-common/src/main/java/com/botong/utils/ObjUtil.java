package com.botong.utils;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

/**
 * <AUTHOR> by zhb on 2023/8/8.
 */

public class ObjUtil {

    private ObjUtil() {
    }

    /**
     * 获取Object对象中的某个属性值
     *
     * @param object object
     * @param key    object中的key值
     * @return key对应的属性值
     */
    public static Object getObjValue(Object object, String key) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            String str = mapper.writeValueAsString(object);
            Map map = mapper.readValue(str, Map.class);
            if (map == null) {
                return null;
            }
            return map.get(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}