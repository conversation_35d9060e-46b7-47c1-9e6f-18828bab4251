spring:
  datasource:
    dynamic:
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: BtoHikariCP
      primary: base
      datasource:
        base:
          username: admin
          password: bto123
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************
        clickhouse:
          username: admin
          password: PFWzk6qYvyRBpNX4
          driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
          url: **************************************
  boot:
    admin:
      client:
        url: http://localhost:8401
        username: bto
        password: btoadmin
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848

  redis:
    cluster:
      nodes: ***************:6379,***************:6379,***************:6379
    database: 0
    host: ***************
    port: 6379
    timeout: 5000
    password: