package com.botong.entity.vo;

import com.botong.enums.ResultCode;

import java.io.Serializable;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class ResultVo<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final int SUCCESS_CODE;
    public static final String SUCCESS_MESSAGE;
    public static final int FAIL_CODE;
    public static final String FAIL_MESSAGE;
    private int code;
    private String message;
    private T data;

    static {
        SUCCESS_CODE = ResultCode.SUCCESS.getCode();
        SUCCESS_MESSAGE = ResultCode.SUCCESS.getMessage();
        FAIL_CODE = ResultCode.FAIL.getCode();
        FAIL_MESSAGE = ResultCode.FAIL.getMessage();
    }

    public ResultVo() {
    }

    public static <T> ResultVo<T> success() {
        return restResult((T) null, SUCCESS_CODE, SUCCESS_MESSAGE);
    }

    public static <T> ResultVo<T> success(ResultCode resultCode) {
        return restResult((T) null, resultCode.getCode(), resultCode.getMessage());
    }

    public static <T> ResultVo<T> success(T data) {
        return restResult(data, SUCCESS_CODE, SUCCESS_MESSAGE);
    }

    public static <T> ResultVo<T> success(T data, String msg) {
        return restResult(data, SUCCESS_CODE, msg);
    }

    public static <T> ResultVo<T> fail() {
        return restResult((T) null, FAIL_CODE, FAIL_MESSAGE);
    }
    public static <T> ResultVo<T> fail(ResultCode resultCode) {
        return restResult((T) null, resultCode.getCode(), resultCode.getMessage());
    }

    public static <T> ResultVo<T> fail(String msg) {
        return restResult((T) null, FAIL_CODE, msg);
    }

    public static <T> ResultVo<T> fail(T data) {
        return restResult(data, FAIL_CODE, FAIL_MESSAGE);
    }

    public static <T> ResultVo<T> fail(T data, String msg) {
        return restResult(data, FAIL_CODE, msg);
    }

    public static <T> ResultVo<T> fail(int code, String msg) {
        return restResult((T) null, code, msg);
    }

    private static <T> ResultVo<T> restResult(T data, int code, String msg) {
        ResultVo<T> apiResult = new ResultVo<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMessage(msg);
        return apiResult;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return SUCCESS_CODE == this.getCode();
    }

}