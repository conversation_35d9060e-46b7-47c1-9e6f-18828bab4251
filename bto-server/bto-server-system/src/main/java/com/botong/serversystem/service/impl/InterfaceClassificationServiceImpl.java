package com.botong.serversystem.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.botong.api.module.serverinterface.InterfaceApi;
import com.botong.constant.BtoConstant;
import com.botong.entity.serverinterface.entity.Classification;
import com.botong.entity.serverinterface.entity.InterfaceClassification;
import com.botong.entity.serverinterface.vo.InterfaceNameVO;
import com.botong.entity.vo.ResultVo;
import com.botong.serversystem.entity.respones.ClassInterfaceTree;
import com.botong.serversystem.entity.respones.ClassificationVO;
import com.botong.serversystem.mapper.InterfaceClassificationMapper;
import com.botong.serversystem.service.ClassificationService;
import com.botong.serversystem.service.InterfaceClassificationService;
import com.botong.utils.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (InterfaceClassification)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-05 09:24:55
 */
@Transactional(rollbackFor = Exception.class)
@Service("interfaceClassificationService")
public class InterfaceClassificationServiceImpl extends ServiceImpl<InterfaceClassificationMapper, InterfaceClassification> implements InterfaceClassificationService {

    @Autowired
    private InterfaceApi interfaceApi;

    @Autowired
    private ClassificationService classificationService;
    @Override
    public ResultVo<List<ClassInterfaceTree>> classInterfaceTree(ClassificationVO classQueryDTO) {
        LambdaQueryWrapper<Classification> classQueryWrapper = new LambdaQueryWrapper<>();
        List<Classification> classifications = classificationService.list(classQueryWrapper);

        List<Long> classIds = classifications.stream()
                .map(Classification::getClassId)
                .collect(Collectors.toList());

        List<ClassInterfaceTree> trees = BeanCopyUtils.copyBeanList(classifications, ClassInterfaceTree.class);

        // 添加未分类的ClassInterfaceTree
        ClassInterfaceTree uncategorizedTree = new ClassInterfaceTree();
        uncategorizedTree.setClassId(BtoConstant.UNDEFINE_CLASS_ID);
        uncategorizedTree.setClassName("未分类");
        uncategorizedTree.setInterfaceTreeList(new ArrayList<>());
        trees.add(uncategorizedTree);

        List<InterfaceNameVO> interfaceTrees = interfaceApi.getInterfaceInfoRepeat().getData();
        if (CollUtil.isNotEmpty(classIds) || CollUtil.isNotEmpty(interfaceTrees)) {
            for (ClassInterfaceTree tree : trees) {
                Long classId = tree.getClassId();
                List<InterfaceNameVO> interfaceList = interfaceTrees.stream()
                        .filter(interfaceTree -> {
                            if (BtoConstant.UNDEFINE_CLASS_ID.equals(classId)) {
                                // 对于未分类的接口，将classId为null的接口都过滤出来
                                return interfaceTree.getClassId() == null;
                            } else {
                                return classId.equals(interfaceTree.getClassId());
                            }
                        })
                        .collect(Collectors.toList());
                tree.setInterfaceTreeList(interfaceList);
            }
        }

        return ResultVo.success(trees);
    }

    @Override
    public ResultVo interfaceClass(Long classId, List<Long> interfaceIds) {
        this.lambdaUpdate().eq(InterfaceClassification::getClassId, classId).remove();
        if (CollUtil.isEmpty(interfaceIds)) {
            return ResultVo.success();
        }
        //去重
        HashSet<Long> newInterfaceIds = new HashSet<>(interfaceIds);
        CollUtil.distinct(interfaceIds);
        List<InterfaceClassification> interfaceClassifications = newInterfaceIds.stream()
                .map(interfaceId -> {
                    InterfaceClassification interfaceClassification = new InterfaceClassification();
                    interfaceClassification.setClassId(classId);
                    interfaceClassification.setInterfaceId(interfaceId);
                    return interfaceClassification;
                })
                .collect(Collectors.toList());
        boolean success = this.saveBatch(interfaceClassifications);
        if (success) {
            return ResultVo.success();
        }
        return ResultVo.fail();
    }

    @Override
    public ResultVo<List<Long>> listByClass(Long classId) {
        List<InterfaceClassification> list = this.lambdaQuery().eq(InterfaceClassification::getClassId, classId).select(InterfaceClassification::getInterfaceId).list();
        List<Long> interfaceIds = list.stream().map(InterfaceClassification::getInterfaceId).collect(Collectors.toList());
        return ResultVo.success(interfaceIds);
    }

    @Override
    public ResultVo deleteByinterface(List<Long> interfaceIds) {
        boolean success = this.lambdaUpdate().in(InterfaceClassification::getInterfaceId, interfaceIds).remove();
        if (success){
            return ResultVo.success();
        }
        return ResultVo.fail();
    }
}

