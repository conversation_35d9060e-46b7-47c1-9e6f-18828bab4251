package com.botong.selector;

import com.botong.configure.BtoAuthExceptionConfigure;
import com.botong.configure.BtoAuth2FeignConfigure;
import com.botong.configure.BtoServerProtectConfigure;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;

/**
 * <AUTHOR> by zhb on 2023/7/13.
 */
public class BtoCloudApplicationSelector implements ImportSelector {

    /**
     * @EnableBtoServerProtect，开启微服务防护，避免客户端绕过网关直接请求微服务；
     * @EnableBtoOauth2FeignClient，开启带令牌的Feign请求，避免微服务内部调用出现401异常；
     * @EnableBtoAuthExceptionHandler，认证类型异常翻译。
     */
    @Override
    public String[] selectImports(AnnotationMetadata annotationMetadata) {
        return new String[]{
                BtoAuthExceptionConfigure.class.getName(),
                BtoAuth2FeignConfigure.class.getName(),
                BtoServerProtectConfigure.class.getName()
        };
    }
}