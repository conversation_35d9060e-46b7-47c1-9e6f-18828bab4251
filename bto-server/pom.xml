<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bto-server</artifactId>
    <name>bto-server</name>
    <description>bto-server</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.botong</groupId>
        <artifactId>data-center-api</artifactId>
        <version>${revision}</version>
    </parent>

    <modules>
        <module>bto-server-system</module>
        <module>bto-server-interface</module>
        <module>bto-server-api</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.botong</groupId>
            <artifactId>bto-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>
    </dependencies>

</project>