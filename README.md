# 数据中心API系统 (Data Center API)

## 项目概述

数据中心API系统是一个基于 Spring Cloud 微服务架构 的企业级光伏数据中心开放平台，主要面向外部系统和资方、合作伙伴提供统一的接口服务。系统通过 OAuth2 认证、网关路由、服务监控 等现代化微服务技术，为光伏行业开放电站运行数据、设备监控信息和用户权限管理能力，帮助企业快速集成和构建上层应用，形成完整的对外数据中心解决方案。

## 系统架构

### 高级架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │────│   API网关        │────│   认证服务       │
│   (Frontend)    │    │  (bto-gateway)  │    │  (bto-oauth)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ├─────────────────────────────────┐
                              │                                 │
                    ┌─────────────────┐              ┌─────────────────┐
                    │   系统服务       │              │   接口服务       │
                    │(bto-server-     │              │(bto-server-     │
                    │ system)         │              │ interface)      │
                    └─────────────────┘              └─────────────────┘
                              │                                 │
                    ┌─────────────────┐              ┌─────────────────┐
                    │   监控服务       │              │   公共组件       │
                    │(bto-monitor)    │              │  (bto-common)   │
                    └─────────────────┘              └─────────────────┘
```

### 核心组件

- **bto-gateway**: API网关，负责路由转发、负载均衡、熔断降级
- **bto-oauth**: OAuth2认证服务，提供用户认证、授权、JWT令牌管理
- **bto-server-system**: 系统管理服务，提供用户、角色、权限、部门管理
- **bto-server-interface**: 业务接口服务，提供光伏电站数据查询、设备监控等API
- **bto-monitor**: 服务监控，基于Spring Boot Admin的微服务监控
- **bto-common**: 公共组件库，提供通用工具类、配置、注解等

## 技术栈

### 后端技术
- **Java 8**: 开发语言
- **Spring Boot 2.2.0**: 微服务框架
- **Spring Cloud Hoxton**: 微服务治理
- **Spring Cloud Alibaba 2.1.1**: 阿里巴巴微服务组件
- **Spring Security OAuth2**: 认证授权
- **Spring Cloud Gateway**: API网关
- **Nacos**: 服务注册发现、配置中心
- **MyBatis Plus 3.5.2**: ORM框架
- **MySQL**: 主数据库
- **ClickHouse**: 时序数据库（用于设备数据存储）
- **Redis**: 缓存、会话存储
- **Hystrix**: 熔断器

### 工具库
- **Hutool 5.4.6**: Java工具库
- **Lombok 1.18.24**: 代码简化
- **FastJSON2 2.0.26**: JSON处理
- **Swagger2 2.9.2**: API文档
- **Knife4j 2.0.9**: API文档增强
- **PageHelper 1.4.2**: 分页插件
- **JWT 3.10.3**: JWT令牌处理

### 监控运维
- **Spring Boot Admin 2.2.0**: 服务监控
- **Logback**: 日志框架
- **Logstash**: 日志收集
- **Maven**: 项目构建

## 项目结构

```
data-center-api/
├── bto-common/                 # 公共组件模块
│   ├── src/main/java/
│   │   └── com/botong/
│   │       ├── annotation/     # 自定义注解
│   │       ├── configure/      # 配置类
│   │       ├── constant/       # 常量定义
│   │       ├── entity/         # 实体类
│   │       ├── enums/          # 枚举类
│   │       ├── exception/      # 异常处理
│   │       ├── selector/       # 选择器
│   │       └── utils/          # 工具类
│   └── pom.xml
├── bto-gateway/                # API网关模块
│   ├── src/main/java/
│   │   └── com/botong/gateway/
│   │       ├── configure/      # 网关配置
│   │       ├── filter/         # 网关过滤器
│   │       └── properties/     # 配置属性
│   ├── src/main/resources/
│   │   ├── application.yml     # 主配置文件
│   │   ├── application-dev.yml # 开发环境配置
│   │   ├── application-test.yml# 测试环境配置
│   │   ├── application-prod.yml# 生产环境配置
│   │   └── logback-spring.xml  # 日志配置
│   └── pom.xml
├── bto-oauth/                  # OAuth2认证模块
│   ├── src/main/java/
│   │   └── com/botong/oauth/
│   │       ├── configure/      # OAuth2配置
│   │       ├── controller/     # 认证控制器
│   │       ├── mapper/         # 数据访问层
│   │       ├── properties/     # 配置属性
│   │       └── service/        # 业务服务层
│   ├── src/main/resources/
│   │   ├── application.yml     # 主配置文件
│   │   ├── application-*.yml   # 环境配置文件
│   │   ├── mapper/             # MyBatis映射文件
│   │   └── logback-spring.xml  # 日志配置
│   └── pom.xml
├── bto-server/                 # 业务服务模块
│   ├── bto-server-api/         # API接口定义
│   ├── bto-server-system/      # 系统管理服务
│   │   ├── src/main/java/
│   │   │   └── com/botong/serversystem/
│   │   │       ├── controller/ # 控制器层
│   │   │       ├── service/    # 业务服务层
│   │   │       ├── mapper/     # 数据访问层
│   │   │       ├── entity/     # 实体类
│   │   │       ├── configure/  # 配置类
│   │   │       └── properties/ # 配置属性
│   │   └── src/main/resources/
│   │       ├── application.yml # 主配置文件
│   │       ├── application-*.yml # 环境配置文件
│   │       ├── mapper/         # MyBatis映射文件
│   │       └── server-system.properties # 模块配置
│   ├── bto-server-interface/   # 业务接口服务
│   │   ├── src/main/java/
│   │   │   └── com/botong/serverinterface/
│   │   │       ├── controller/ # 控制器层
│   │   │       ├── service/    # 业务服务层
│   │   │       ├── mapper/     # 数据访问层
│   │   │       ├── pojo/       # 数据传输对象
│   │   │       ├── feign/      # Feign客户端
│   │   │       ├── configure/  # 配置类
│   │   │       └── properties/ # 配置属性
│   │   └── src/main/resources/
│   │       ├── application.yml # 主配置文件
│   │       ├── application-*.yml # 环境配置文件
│   │       └── server-interface.properties # 模块配置
│   └── pom.xml
├── bto-monitor/                # 监控模块
│   ├── bto-monitor-admin/      # 监控管理服务
│   │   ├── src/main/java/
│   │   │   └── com/botong/admin/
│   │   └── src/main/resources/
│   │       ├── application.yml # 主配置文件
│   │       └── logback-spring.xml # 日志配置
│   └── pom.xml
├── log/                        # 日志目录
│   ├── bto-gateway/           # 网关日志
│   ├── bto-oauth/             # 认证服务日志
│   ├── bto-server-system/     # 系统服务日志
│   ├── bto-server-interface/  # 接口服务日志
│   └── bto-monitor-admin/     # 监控服务日志
└── pom.xml                     # 根项目配置文件
```

## 安装说明

### 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 5.0+
- **ClickHouse**: 20.0+ (可选，用于时序数据)
- **Nacos**: 1.4+ (服务注册发现)

### 基础环境搭建

#### 1. 安装JDK 8
```bash
# 下载并安装JDK 8
# 配置JAVA_HOME环境变量
export JAVA_HOME=/path/to/jdk8
export PATH=$JAVA_HOME/bin:$PATH
```

#### 2. 安装Maven
```bash
# 下载并安装Maven 3.6+
# 配置MAVEN_HOME环境变量
export MAVEN_HOME=/path/to/maven
export PATH=$MAVEN_HOME/bin:$PATH
```

#### 3. 安装MySQL
```bash
# 安装MySQL 5.7+
# 创建数据库
CREATE DATABASE data_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 4. 安装Redis
```bash
# 安装Redis 5.0+
# 启动Redis服务
redis-server
```

#### 5. 安装Nacos
```bash
# 下载Nacos 1.4+
# 启动Nacos服务
sh startup.sh -m standalone
```

### 项目构建

#### 1. 克隆项目
```bash
git clone <repository-url>
cd data-center-api
```

#### 2. 编译项目
```bash
# 编译整个项目
mvn clean compile

# 打包项目
mvn clean package -DskipTests
```

#### 3. 安装依赖
```bash
# 安装到本地仓库
mvn clean install -DskipTests
```

## 配置说明

### 环境配置

项目支持多环境配置：
- `dev`: 开发环境
- `test`: 测试环境  
- `prod`: 生产环境

### 核心配置项

#### 数据库配置
```yaml
spring:
  datasource:
    dynamic:
      primary: base
      datasource:
        base:
          username: admin
          password: your_password
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************
        clickhouse: # 可选，用于时序数据
          username: admin
          password: your_password
          driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
          url: ***********************************
```

#### Redis配置
```yaml
spring:
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    timeout: 5000
    password: your_redis_password
```

#### Nacos配置
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
```

### 服务端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| bto-gateway | 8301 | API网关 |
| bto-oauth | 8101 | OAuth2认证服务 |
| bto-server-system | 8201 | 系统管理服务 |
| bto-server-interface | 8202 | 业务接口服务 |
| bto-monitor-admin | 8401 | 监控管理服务 |

## 使用示例

### 启动服务

#### 1. 启动基础服务
```bash
# 启动Nacos
cd nacos/bin
sh startup.sh -m standalone

# 启动Redis
redis-server

# 启动MySQL
systemctl start mysql
```

#### 2. 启动应用服务

按以下顺序启动服务：

```bash
# 1. 启动认证服务
cd bto-oauth
mvn spring-boot:run

# 2. 启动网关服务
cd bto-gateway  
mvn spring-boot:run

# 3. 启动系统管理服务
cd bto-server/bto-server-system
mvn spring-boot:run

# 4. 启动业务接口服务
cd bto-server/bto-server-interface
mvn spring-boot:run

# 5. 启动监控服务
cd bto-monitor/bto-monitor-admin
mvn spring-boot:run
```

### API访问示例

#### 1. 获取访问令牌
```bash
curl -X POST "http://localhost:8301/auth/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic <client_credentials>" \
  -d "grant_type=password&username=admin&password=123456"
```

#### 2. 访问业务API
```bash
# 查询电站信息
curl -X POST "http://localhost:8301/interface/api/plant" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <access_token>" \
  -d '{
    "special": 1,
    "currentPage": 1,
    "size": 10
  }'

# 查询逆变器信息
curl -X POST "http://localhost:8301/interface/api/inverter" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <access_token>" \
  -d '{
    "special": 1,
    "currentPage": 1,
    "size": 10
  }'
```

#### 3. 系统管理API
```bash
# 获取用户列表
curl -X GET "http://localhost:8301/system/user/list" \
  -H "Authorization: Bearer <access_token>"

# 获取角色列表  
curl -X GET "http://localhost:8301/system/role/list" \
  -H "Authorization: Bearer <access_token>"
```

### 访问监控面板

- **服务监控**: http://localhost:8401 (用户名: bto, 密码: btoadmin)
- **API文档**: 
  - 系统服务: http://localhost:8301/system/swagger-ui.html
  - 接口服务: http://localhost:8301/interface/swagger-ui.html
  - 认证服务: http://localhost:8301/auth/swagger-ui.html

## 开发指南

### 开发环境搭建

#### 1. IDE配置
推荐使用IntelliJ IDEA：
- 安装Lombok插件
- 配置Maven设置
- 导入代码格式化规则

#### 2. 本地开发配置
```bash
# 修改各服务的application.yml，激活dev环境
spring:
  profiles:
    active: dev
```

#### 3. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE data_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本（如果有）
-- source /path/to/init.sql
```

### 代码规范

#### 1. 包结构规范
```
com.botong.{module}
├── controller/     # 控制器层
├── service/        # 业务服务层
│   └── impl/       # 服务实现
├── mapper/         # 数据访问层
├── entity/         # 实体类
├── pojo/           # 数据传输对象
│   ├── dto/        # 请求对象
│   └── vo/         # 响应对象
├── configure/      # 配置类
├── properties/     # 配置属性
└── utils/          # 工具类
```

#### 2. 命名规范
- 类名：大驼峰命名法 (PascalCase)
- 方法名：小驼峰命名法 (camelCase)  
- 常量：全大写下划线分隔 (UPPER_SNAKE_CASE)
- 包名：全小写点分隔

#### 3. 注解使用
```java
// 控制器类
@RestController
@RequestMapping("api")
@Api(tags = "API接口")
public class ApiController {
    
    @ApiOperation("接口描述")
    @PostMapping("/endpoint")
    public ResultVo<Object> endpoint(@RequestBody RequestDTO request) {
        // 实现逻辑
    }
}

// 服务类
@Service
@Transactional(rollbackFor = Exception.class)
public class ServiceImpl implements Service {
    // 实现逻辑
}
```

### 构建流程

#### 1. 本地构建
```bash
# 编译
mvn clean compile

# 运行测试
mvn test

# 打包
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests
```

#### 2. 多模块构建
```bash
# 构建指定模块
mvn clean package -pl bto-gateway -am

# 构建多个模块
mvn clean package -pl bto-gateway,bto-oauth -am
```

### 贡献指南

#### 1. 分支管理
- `master`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于新功能开发
- `hotfix/*`: 热修复分支，用于紧急修复

#### 2. 提交规范
```bash
# 提交格式
git commit -m "type(scope): description"

# 示例
git commit -m "feat(auth): add JWT token refresh"
git commit -m "fix(gateway): resolve routing issue"
git commit -m "docs(readme): update installation guide"
```

#### 3. 代码审查
- 所有代码变更需要通过Pull Request
- 至少需要一个审查者批准
- 确保所有测试通过
- 遵循代码规范

## API文档

### 认证服务 (bto-oauth)

#### 获取访问令牌
```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic <client_credentials>

grant_type=password&username=<username>&password=<password>
```

**响应示例:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 7199,
  "scope": "all"
}
```

#### 获取用户信息
```http
GET /user
Authorization: Bearer <access_token>
```

#### 退出登录
```http
POST /logout
Authorization: Bearer <access_token>
```

#### 获取验证码
```http
GET /captcha?key=<timestamp>&responseType=picture
```

### 系统管理服务 (bto-server-system)

#### 用户管理

**获取用户列表**
```http
GET /user/list?currentPage=1&size=10
Authorization: Bearer <access_token>
```

**添加用户**
```http
POST /user/add
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "email": "<EMAIL>",
  "deptId": 1,
  "roleIds": [1, 2]
}
```

**更新用户**
```http
PUT /user/update
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "userId": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "deptId": 1,
  "roleIds": [1, 2]
}
```

**删除用户**
```http
DELETE /user/delete
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "userIds": [1, 2, 3]
}
```

#### 角色管理

**获取角色列表**
```http
GET /role/list?currentPage=1&size=10
Authorization: Bearer <access_token>
```

**添加角色**
```http
POST /role/add
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "roleName": "测试角色",
  "roleCode": "test_role",
  "remark": "测试角色描述",
  "menuIds": [1, 2, 3]
}
```

#### 部门管理

**获取部门树**
```http
GET /dept/list
Authorization: Bearer <access_token>
```

**添加部门**
```http
POST /dept/add
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "deptName": "测试部门",
  "parentId": 0,
  "orderNum": 1,
  "status": "0"
}
```

### 业务接口服务 (bto-server-interface)

#### 电站管理

**查询电站信息**
```http
POST /api/plant
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "special": 1,
  "currentPage": 1,
  "size": 10,
  "plantName": "电站名称"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "plantId": 1,
        "plantName": "测试电站",
        "capacity": 1000.0,
        "location": "测试地址",
        "status": 1
      }
    ],
    "total": 1,
    "currentPage": 1,
    "size": 10
  }
}
```

#### 逆变器管理

**查询逆变器信息**
```http
POST /api/inverter
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "special": 1,
  "currentPage": 1,
  "size": 10,
  "deviceSn": "设备序列号"
}
```

**获取历史发电记录**
```http
POST /api/getHistoryElectricity
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "special": 1,
  "currentPage": 1,
  "size": 10,
  "startTime": "2023-01-01",
  "endTime": "2023-12-31",
  "deviceSn": "设备序列号"
}
```

#### 工单管理

**查询工单信息**
```http
POST /api/work
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "currentPage": 1,
  "size": 10,
  "status": 1,
  "workOrderType": "维修"
}
```

#### 告警管理

**查询逆变器告警**
```http
POST /api/getInverterAlarm
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "special": 1,
  "currentPage": 1,
  "size": 10,
  "startTime": "2023-01-01",
  "endTime": "2023-12-31"
}
```

### 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "code": 200,           // 状态码：200成功，其他失败
  "message": "操作成功",  // 响应消息
  "data": {},            // 响应数据
  "timestamp": 1640995200000  // 时间戳
}
```

### 分页响应格式

分页查询接口的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [],          // 数据列表
    "total": 100,        // 总记录数
    "currentPage": 1,    // 当前页码
    "size": 10,          // 每页大小
    "totalPages": 10     // 总页数
  }
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 测试

### 单元测试

项目使用JUnit进行单元测试：

```bash
# 运行所有测试
mvn test

# 运行指定模块测试
mvn test -pl bto-server-system

# 运行指定测试类
mvn test -Dtest=UserServiceTest

# 运行指定测试方法
mvn test -Dtest=UserServiceTest#testFindUser
```

### 集成测试

#### 1. 测试环境配置

修改测试环境配置文件 `application-test.yml`：

```yaml
spring:
  profiles:
    active: test
  datasource:
    dynamic:
      primary: base
      datasource:
        base:
          url: ********************************************
          username: test_user
          password: test_password
  redis:
    host: localhost
    port: 6379
    database: 1
```

#### 2. 运行集成测试

```bash
# 启动测试环境
mvn spring-boot:run -Dspring.profiles.active=test

# 使用Postman或curl进行API测试
curl -X POST "http://localhost:8301/auth/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=admin&password=123456"
```

### 测试策略

#### 1. 测试分层
- **单元测试**: 测试单个方法或类的功能
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的业务流程

#### 2. 测试覆盖率
- 目标覆盖率: 80%以上
- 关键业务逻辑: 90%以上
- 工具类和配置类: 60%以上

#### 3. 测试数据管理
- 使用测试专用数据库
- 每次测试前清理数据
- 使用事务回滚保证数据隔离

### 性能测试

#### 1. 压力测试工具
推荐使用JMeter进行压力测试：

```bash
# 安装JMeter
wget https://jmeter.apache.org/download_jmeter.cgi
tar -xzf apache-jmeter-*.tgz

# 运行压力测试
jmeter -n -t test-plan.jmx -l results.jtl
```

#### 2. 性能指标
- **响应时间**: 平均响应时间 < 200ms
- **吞吐量**: QPS > 1000
- **错误率**: < 0.1%
- **资源使用**: CPU < 80%, 内存 < 80%

## 部署

### 开发环境部署

#### 1. 本地部署
```bash
# 启动所有服务
./start-all.sh

# 或者逐个启动
cd bto-oauth && mvn spring-boot:run &
cd bto-gateway && mvn spring-boot:run &
cd bto-server/bto-server-system && mvn spring-boot:run &
cd bto-server/bto-server-interface && mvn spring-boot:run &
cd bto-monitor/bto-monitor-admin && mvn spring-boot:run &
```

#### 2. Docker部署

**创建Dockerfile:**
```dockerfile
FROM openjdk:8-jre-alpine
VOLUME /tmp
COPY target/*.jar app.jar
ENTRYPOINT ["java","-jar","/app.jar"]
EXPOSE 8080
```

**构建镜像:**
```bash
# 构建各服务镜像
docker build -t bto-gateway:latest ./bto-gateway
docker build -t bto-oauth:latest ./bto-oauth
docker build -t bto-server-system:latest ./bto-server/bto-server-system
docker build -t bto-server-interface:latest ./bto-server/bto-server-interface
docker build -t bto-monitor-admin:latest ./bto-monitor/bto-monitor-admin
```

**Docker Compose部署:**
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:5.7
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: data_center
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:5.0-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis123

  nacos:
    image: nacos/nacos-server:1.4.2
    environment:
      MODE: standalone
    ports:
      - "8848:8848"

  bto-oauth:
    image: bto-oauth:latest
    ports:
      - "8101:8101"
    depends_on:
      - mysql
      - redis
      - nacos
    environment:
      SPRING_PROFILES_ACTIVE: prod

  bto-gateway:
    image: bto-gateway:latest
    ports:
      - "8301:8301"
    depends_on:
      - bto-oauth
      - nacos
    environment:
      SPRING_PROFILES_ACTIVE: prod

  bto-server-system:
    image: bto-server-system:latest
    ports:
      - "8201:8201"
    depends_on:
      - mysql
      - redis
      - nacos
    environment:
      SPRING_PROFILES_ACTIVE: prod

  bto-server-interface:
    image: bto-server-interface:latest
    ports:
      - "8202:8202"
    depends_on:
      - mysql
      - redis
      - nacos
    environment:
      SPRING_PROFILES_ACTIVE: prod

  bto-monitor-admin:
    image: bto-monitor-admin:latest
    ports:
      - "8401:8401"
    depends_on:
      - nacos
    environment:
      SPRING_PROFILES_ACTIVE: prod

volumes:
  mysql_data:
```

**启动服务:**
```bash
docker-compose up -d
```

### 生产环境部署

#### 1. 服务器要求
- **CPU**: 4核以上
- **内存**: 8GB以上
- **磁盘**: 100GB以上SSD
- **网络**: 100Mbps以上带宽

#### 2. 环境准备
```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash -s docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 创建应用目录
mkdir -p /opt/data-center-api
cd /opt/data-center-api
```

#### 3. 配置优化

**JVM参数优化:**
```bash
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError"
```

**数据库连接池优化:**
```yaml
spring:
  datasource:
    dynamic:
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 20
        min-idle: 10
        connection-test-query: select 1
```

**Redis连接池优化:**
```yaml
spring:
  redis:
    lettuce:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 200
        max-wait: 10000
```

#### 4. 监控配置

**Prometheus监控:**
```yaml
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
```

**日志配置:**
```xml
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/opt/logs/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/opt/logs/application.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

#### 5. 部署脚本

**部署脚本 (deploy.sh):**
```bash
#!/bin/bash

# 设置变量
APP_NAME="data-center-api"
VERSION="1.0.0"
DEPLOY_DIR="/opt/${APP_NAME}"

# 创建部署目录
mkdir -p ${DEPLOY_DIR}
cd ${DEPLOY_DIR}

# 停止旧服务
docker-compose down

# 拉取最新镜像
docker-compose pull

# 启动服务
docker-compose up -d

# 等待服务启动
sleep 30

# 健康检查
curl -f http://localhost:8301/actuator/health || exit 1

echo "部署完成！"
```

**回滚脚本 (rollback.sh):**
```bash
#!/bin/bash

# 回滚到上一个版本
docker-compose down
docker-compose -f docker-compose.backup.yml up -d

echo "回滚完成！"
```

#### 6. 负载均衡

**Nginx配置:**
```nginx
upstream data-center-gateway {
    server 127.0.0.1:8301 weight=1 max_fails=2 fail_timeout=30s;
    server 127.0.0.1:8302 weight=1 max_fails=2 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.example.com;
    
    location / {
        proxy_pass http://data-center-gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

### 高可用部署

#### 1. 数据库高可用
- 使用MySQL主从复制
- 配置读写分离
- 定期备份数据

#### 2. Redis高可用
- 使用Redis Sentinel
- 配置主从复制
- 启用持久化

#### 3. 应用高可用
- 多实例部署
- 使用负载均衡
- 配置健康检查

#### 4. 监控告警
- 配置Prometheus + Grafana
- 设置告警规则
- 集成钉钉/邮件通知

## 故障排除

### 常见问题

#### 1. 服务启动失败

**问题**: 服务启动时报端口被占用
```
Port 8301 was already in use
```

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :8301
netstat -tulpn | grep 8301

# 杀死进程
kill -9 <PID>

# 或者修改配置文件中的端口
```

**问题**: 数据库连接失败
```
Could not create connection to database server
```

**解决方案**:
```bash
# 检查数据库服务状态
systemctl status mysql

# 检查数据库连接参数
mysql -h localhost -u admin -p data_center

# 检查防火墙设置
firewall-cmd --list-ports
```

#### 2. 认证相关问题

**问题**: 获取token失败
```
{
  "error": "invalid_client",
  "error_description": "Bad client credentials"
}
```

**解决方案**:
```bash
# 检查客户端配置
# 确认client_id和client_secret正确
# 检查OAuth2配置类

# 查看认证服务日志
docker logs bto-oauth
tail -f /opt/logs/bto-oauth/error.log
```

**问题**: Token过期
```
{
  "error": "invalid_token",
  "error_description": "Access token expired"
}
```

**解决方案**:
```bash
# 使用refresh_token刷新访问令牌
curl -X POST "http://localhost:8301/auth/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=refresh_token&refresh_token=<refresh_token>"
```

#### 3. 网关路由问题

**问题**: 路由转发失败
```
503 Service Unavailable
```

**解决方案**:
```bash
# 检查目标服务状态
curl http://localhost:8201/actuator/health

# 检查Nacos服务注册
curl http://localhost:8848/nacos/v1/ns/catalog/services

# 查看网关日志
docker logs bto-gateway
```

#### 4. 数据库相关问题

**问题**: 连接池耗尽
```
HikariPool-1 - Connection is not available
```

**解决方案**:
```yaml
# 调整连接池配置
spring:
  datasource:
    dynamic:
      hikari:
        max-pool-size: 30
        min-idle: 10
        connection-timeout: 60000
```

**问题**: 慢查询
```
Slow query detected: SELECT * FROM large_table
```

**解决方案**:
```sql
-- 添加索引
CREATE INDEX idx_column_name ON table_name(column_name);

-- 优化查询语句
SELECT specific_columns FROM table_name WHERE indexed_column = ?;

-- 分页查询
SELECT * FROM table_name LIMIT 10 OFFSET 0;
```

#### 5. Redis相关问题

**问题**: Redis连接超时
```
RedisConnectionFailureException: Unable to connect to Redis
```

**解决方案**:
```bash
# 检查Redis服务状态
redis-cli ping

# 检查Redis配置
redis-cli config get timeout
redis-cli config get maxclients

# 调整连接池配置
spring:
  redis:
    timeout: 10000
    lettuce:
      pool:
        max-active: 200
        max-wait: 10000
```

#### 6. 内存相关问题

**问题**: 内存溢出
```
java.lang.OutOfMemoryError: Java heap space
```

**解决方案**:
```bash
# 调整JVM参数
export JAVA_OPTS="-Xms4g -Xmx8g -XX:+HeapDumpOnOutOfMemoryError"

# 分析内存使用
jmap -histo <PID>
jstat -gc <PID> 1s

# 生成堆转储文件
jmap -dump:format=b,file=heapdump.hprof <PID>
```

### 日志分析

#### 1. 日志级别配置
```yaml
logging:
  level:
    com.botong: DEBUG
    org.springframework.security: DEBUG
    org.springframework.cloud.gateway: DEBUG
```

#### 2. 关键日志位置
- 应用日志: `/opt/logs/{service-name}/`
- 访问日志: `/opt/logs/access/`
- 错误日志: `/opt/logs/error/`

#### 3. 日志分析命令
```bash
# 查看实时日志
tail -f /opt/logs/bto-gateway/info.log

# 搜索错误日志
grep -i "error" /opt/logs/*/error.log

# 统计访问量
awk '{print $1}' access.log | sort | uniq -c | sort -nr

# 查看响应时间
awk '{print $NF}' access.log | sort -n | tail -10
```

### 性能调优

#### 1. JVM调优
```bash
# G1垃圾收集器配置
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
-XX:+G1UseAdaptiveIHOP
-XX:G1MixedGCCountTarget=8

# 内存配置
-Xms4g
-Xmx8g
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m

# GC日志
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/opt/logs/gc.log
```

#### 2. 数据库调优
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 分析查询计划
EXPLAIN SELECT * FROM table_name WHERE condition;

-- 优化索引
SHOW INDEX FROM table_name;
CREATE INDEX idx_name ON table_name(column_name);
```

#### 3. 缓存优化
```yaml
# Redis配置优化
spring:
  redis:
    lettuce:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 200
    timeout: 5000
```

### 监控告警

#### 1. 健康检查端点
```bash
# 检查服务健康状态
curl http://localhost:8301/actuator/health

# 检查服务信息
curl http://localhost:8301/actuator/info

# 检查指标
curl http://localhost:8301/actuator/metrics
```

#### 2. 告警规则配置
```yaml
# Prometheus告警规则
groups:
  - name: data-center-api
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
```

#### 3. 日志监控
```bash
# 监控错误日志
tail -f /opt/logs/*/error.log | grep -i "exception\|error"

# 监控访问日志
tail -f /opt/logs/access.log | awk '{if($9>=400) print $0}'
```

### 备份恢复

#### 1. 数据库备份
```bash
# 全量备份
mysqldump -u admin -p data_center > backup_$(date +%Y%m%d).sql

# 增量备份
mysqlbinlog --start-datetime="2023-01-01 00:00:00" /var/log/mysql/mysql-bin.000001 > incremental_backup.sql

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u admin -p data_center | gzip > ${BACKUP_DIR}/backup_${DATE}.sql.gz
find ${BACKUP_DIR} -name "backup_*.sql.gz" -mtime +7 -delete
```

#### 2. 配置备份
```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz /opt/data-center-api/config/

# 备份Docker配置
cp docker-compose.yml docker-compose.backup.yml
```

#### 3. 恢复流程
```bash
# 数据库恢复
mysql -u admin -p data_center < backup_20231201.sql

# 配置恢复
tar -xzf config_backup_20231201.tar.gz -C /

# 服务重启
docker-compose restart
```

---

## 联系信息

- **项目维护者**: 博通开发团队
- **邮箱**: <EMAIL>
- **网站**: https://open.btosolarman.com/
- **技术支持**: 请通过GitHub Issues提交问题

## 许可证

本项目采用 Apache 2.0 许可证。详情请参阅 [LICENSE](https://www.apache.org/licenses/LICENSE-2.0.html) 文件。

---

*最后更新时间: 2023年12月*
